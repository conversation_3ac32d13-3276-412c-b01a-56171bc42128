import type { Config } from "tailwindcss";

// Safelist for dynamically generated TLD styles
const tldStyles = [
  'bg-primary-100', 'text-primary-700', 'border-primary-200', // .com
  'bg-teal-100', 'text-teal-700', 'border-teal-200', // .net
  'bg-green-100', 'text-green-700', 'border-green-200', // .org
  'bg-violet-100', 'text-violet-700', 'border-violet-200', // .io
  'bg-fuchsia-100', 'text-fuchsia-700', 'border-fuchsia-200', // .app
  'bg-emerald-100', 'text-emerald-700', 'border-emerald-200', // .dev
  'bg-orange-100', 'text-orange-700', 'border-orange-200', // .me
  'bg-amber-100', 'text-amber-700', 'border-amber-200', // .shop
  'bg-indigo-100', 'text-indigo-700', 'border-indigo-200', // .online
  'bg-gray-100', 'text-gray-700', 'border-gray-200' // default/fallback
];

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./client/src/**/*.{js,jsx,ts,tsx}"
  ],
  safelist: [...tldStyles],
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          50: "#f0f7ff",
          100: "#e0efff",
          200: "#bae0ff",
          300: "#7cc6ff",
          400: "#36a9ff",
          500: "#0d8af7",
          600: "#0069e5",
          700: "#0053bc",
          800: "#004099",
          900: "#00367d",
          950: "#002155",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
          50: "#f5f3ff",
          100: "#ede8ff",
          200: "#dcd5ff",
          300: "#c3b4fe",
          400: "#a587fc",
          500: "#8a55f7",
          600: "#7c3ded",
          700: "#6928d1",
          800: "#5922ae",
          900: "#4a1e8c",
          950: "#2e1066",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
          teal: {
            100: "#ccfbf1",
            500: "#14b8a6",
            900: "#134e4a",
          },
          amber: {
            100: "#fef3c7",
            500: "#f59e0b",
            900: "#78350f",
          },
          rose: {
            100: "#ffe4e6",
            500: "#f43f5e",
            900: "#881337",
          },
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "pulse-delay": {
          "0%, 100%": {
            opacity: "0.2",
            transform: "scale(1)",
          },
          "50%": {
            opacity: "0.5",
            transform: "scale(1.05)",
          },
        },
        "ping-slow": {
          "0%": {
            transform: "scale(0.95)",
            opacity: "0.5",
          },
          "70%, 100%": {
            transform: "scale(1.2)",
            opacity: "0",
          },
        },
        "shine": {
          "100%": {
            left: "125%"
          },
        },
        "float": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-10px)" },
        },
        "spin-slow": {
          "0%": { transform: "rotate(0deg)" },
          "100%": { transform: "rotate(360deg)" },
        },
        "float-slow": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-5px)" },
        },
        "scale-in": {
          "0%": { opacity: "0", transform: "scale(0.9)" },
          "100%": { opacity: "1", transform: "scale(1)" },
        },
        "slide-up": {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "slide-down": {
          "0%": { opacity: "0", transform: "translateY(-10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "slide-left": {
          "0%": { opacity: "0", transform: "translateX(10px)" },
          "100%": { opacity: "1", transform: "translateX(0)" },
        },
        "slide-right": {
          "0%": { opacity: "0", transform: "translateX(-10px)" },
          "100%": { opacity: "1", transform: "translateX(0)" },
        },
        "wiggle": {
          "0%, 100%": { transform: "rotate(-2deg)" },
          "50%": { transform: "rotate(2deg)" },
        },
        "flip": {
          "0%": { transform: "perspective(400px) rotateY(0)" },
          "100%": { transform: "perspective(400px) rotateY(180deg)" },
        },
        "flip-back": {
          "0%": { transform: "perspective(400px) rotateY(180deg)" },
          "100%": { transform: "perspective(400px) rotateY(0)" },
        },
        "particles": {
          "0%": { transform: "translateY(0) rotate(0)", opacity: "1" },
          "100%": { transform: "translateY(-100px) rotate(360deg)", opacity: "0" },
        },
        "celebrate": {
          "0%": { transform: "scale(0)", opacity: "0" },
          "50%": { opacity: "1" },
          "100%": { transform: "scale(1.2)", opacity: "0" },
        },
        "progress-pulse": {
          "0%": { opacity: "0.85" },
          "50%": { opacity: "1" },
          "100%": { opacity: "0.85" },
        },
        "typewriter": {
          "0%": { width: "0%" },
          "100%": { width: "100%" }
        },
        "blink": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0" }
        },
        "gradient": {
          "0%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
          "100%": { backgroundPosition: "0% 50%" }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "pulse-delay": "pulse-delay 3s ease-in-out infinite 0.5s",
        "ping-slow": "ping-slow 2.5s cubic-bezier(0, 0, 0.2, 1) infinite",
        "shine": "shine 1.2s ease-in-out infinite",
        "float": "float 3s ease-in-out infinite",
        "spin-slow": "spin-slow 8s linear infinite",
        "float-slow": "float-slow 6s ease-in-out infinite",
        "scale-in": "scale-in 0.5s ease-out",
        "slide-up": "slide-up 0.5s ease-out",
        "slide-down": "slide-down 0.5s ease-out",
        "slide-left": "slide-left 0.5s ease-out",
        "slide-right": "slide-right 0.5s ease-out",
        "wiggle": "wiggle 1s ease-in-out infinite",
        "flip": "flip 0.6s ease-out forwards",
        "flip-back": "flip-back 0.6s ease-out forwards",
        "particles": "particles 2s ease-in-out forwards",
        "celebrate": "celebrate 0.75s ease-out forwards",
        "progress-pulse": "progress-pulse 2s ease-in-out infinite",
        "typewriter": "typewriter 3s steps(40, end) forwards",
        "blink": "blink 1s step-end infinite",
        "gradient": "gradient 3s ease infinite",
      },
      boxShadow: {
        'neum': '5px 5px 10px #d1d9e6, -5px -5px 10px #ffffff',
        'neum-sm': '3px 3px 6px #d1d9e6, -3px -3px 6px #ffffff',
        '3d': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), inset 0 -2px 0 rgba(0, 0, 0, 0.1)',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.07)',
      },
      backdropFilter: {
        'glass': 'blur(4px)',
      },
      backgroundSize: {
        'size-200': '200% 200%',
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;
