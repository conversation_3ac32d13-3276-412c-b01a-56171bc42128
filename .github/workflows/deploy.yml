name: Build and Deploy

on:
  push:
    branches: [ main, ssg-domainmate ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        echo "Installing dependencies..."
        timeout 300 npm install --no-audit --no-fund || echo "npm install timed out, continuing anyway"
        echo "Dependencies installed."

    - name: Build SSG application
      run: |
        echo "Building SSG application..."
        # Set environment variables for build
        export NEXT_PUBLIC_SITE_URL="https://domainmate.net"
        export NEXT_PUBLIC_AUTH0_DOMAIN="${{ secrets.NEXT_PUBLIC_AUTH0_DOMAIN }}"
        export NEXT_PUBLIC_AUTH0_CLIENT_ID="${{ secrets.NEXT_PUBLIC_AUTH0_CLIENT_ID }}"
        export NEXT_PUBLIC_AUTH0_AUDIENCE="${{ secrets.NEXT_PUBLIC_AUTH0_AUDIENCE }}"

        # Build the static site
        npm run build
        echo "SSG build completed."

    - name: Build API server
      run: |
        echo "Building API server..."
        # Build the Express.js API server
        npm run build:server
        echo "API server build completed."

    - name: Create runtime config file
      run: |
        echo "Creating runtime config file..."
        cat > out/config.js << EOF
        window.ENV = {
          SITE_URL: "https://domainmate.net",
          AUTH0_DOMAIN: "${{ secrets.NEXT_PUBLIC_AUTH0_DOMAIN }}",
          AUTH0_CLIENT_ID: "${{ secrets.NEXT_PUBLIC_AUTH0_CLIENT_ID }}",
          AUTH0_AUDIENCE: "${{ secrets.NEXT_PUBLIC_AUTH0_AUDIENCE }}"
        };
        EOF
        echo "Runtime config file created."

    - name: Update index.html
      run: |
        echo "Updating index.html..."
        sed -i '/<\/head>/i \    <script src="/config.js"></script>' out/index.html
        echo "index.html updated."

    - name: Create static file server
      run: |
        echo "Creating static file server..."
        mkdir -p server-static
        cat > server-static/server.js << 'EOF'
        const express = require('express');
        const path = require('path');
        const { createProxyMiddleware } = require('http-proxy-middleware');
        const app = express();
        const port = process.env.PORT || 3000;

        // Proxy API requests to the API server
        app.use('/api', createProxyMiddleware({
          target: 'http://localhost:3001',
          changeOrigin: true,
          timeout: 30000,
          proxyTimeout: 30000,
          onError: (err, req, res) => {
            console.error('Proxy error:', err);
            res.status(500).json({ message: 'API server unavailable' });
          }
        }));

        // Serve static files from out directory
        app.use(express.static(path.join(__dirname, '..', 'out')));

        // Handle client-side routing - serve index.html for all routes
        app.get('*', (req, res) => {
          res.sendFile(path.join(__dirname, '..', 'out', 'index.html'));
        });

        app.listen(port, () => {
          console.log(`Static server running on port ${port}`);
        });
        EOF

        cat > server-static/package.json << 'EOF'
        {
          "name": "domainmate-static-server",
          "version": "1.0.0",
          "main": "server.js",
          "dependencies": {
            "express": "^4.21.2",
            "http-proxy-middleware": "^2.0.6"
          }
        }
        EOF
        echo "Static file server created."

    - name: Create deployment package
      run: |
        echo "Creating deployment package..."
        mkdir -p deployment
        # Copy static files
        cp -r out deployment/
        # Copy static server
        cp -r server-static deployment/
        # Copy API server
        cp -r dist deployment/
        # Copy database migrations and scripts
        cp -r migrations deployment/
        cp -r scripts deployment/
        # Copy essential files
        cp package.json package-lock.json deployment/
        cd deployment && tar -czf ../domainmate-deploy.tar.gz .
        echo "Deployment package created."

    - name: Set up SSH
      run: |
        echo "Setting up SSH..."
        mkdir -p ~/.ssh
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.VPS_IP }} >> ~/.ssh/known_hosts
        echo "SSH setup completed."

    - name: Create systemd service files
      run: |
        echo "Creating systemd service files..."
        
        # Static file server service
        cat > domainmate-static.service << EOF
        [Unit]
        Description=DomainMate Static File Server
        After=network.target domainmate-api.service
        Requires=domainmate-api.service

        [Service]
        Type=simple
        User=${{ secrets.VPS_USERNAME }}
        WorkingDirectory=/root/domainmate/server-static
        ExecStart=node /root/domainmate/server-static/server.js
        Restart=on-failure
        Environment=NODE_ENV=production
        Environment=PORT=3000

        [Install]
        WantedBy=multi-user.target
        EOF

        # API server service
        cat > domainmate-api.service << EOF
        [Unit]
        Description=DomainMate API Server
        After=network.target

        [Service]
        Type=simple
        User=${{ secrets.VPS_USERNAME }}
        WorkingDirectory=/root/domainmate
        ExecStart=node /root/domainmate/dist/index.js
        Restart=on-failure
        Environment=NODE_ENV=production
        Environment=PORT=3001
        Environment=DATABASE_URL=${{ secrets.DATABASE_URL }}
        Environment=OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
        Environment=GEMINI_API_KEY=${{ secrets.GEMINI_API_KEY }}
        Environment=AI_PROVIDER=${{ secrets.AI_PROVIDER }}
        Environment=AUTH0_DOMAIN=${{ secrets.AUTH0_DOMAIN }}
        Environment=AUTH0_AUDIENCE=${{ secrets.AUTH0_AUDIENCE }}

        [Install]
        WantedBy=multi-user.target
        EOF
        
        echo "Systemd service files created."

    - name: Transfer files to VPS
      run: |
        echo "Starting file transfer to VPS..."
        timeout 60 scp domainmate-deploy.tar.gz domainmate-static.service domainmate-api.service ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }}:~/
        echo "File transfer completed."

    - name: Create directory and extract files on VPS
      run: |
        echo "Creating directory and extracting files..."
        timeout 120 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "mkdir -p ~/domainmate && tar -xzf ~/domainmate-deploy.tar.gz -C ~/domainmate"
        echo "Files extracted successfully."

    - name: Install dependencies on VPS
      run: |
        echo "Installing dependencies..."
        timeout 300 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "cd ~/domainmate && npm ci --production --no-fund"
        timeout 300 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "cd ~/domainmate/server-static && npm install --production --no-fund"
        echo "Dependencies installed."

    - name: Run database migrations
      run: |
        echo "Running database migrations..."
        timeout 120 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "cd ~/domainmate && npm run db:push:all"
        echo "Database migrations completed."

    - name: Configure systemd services
      run: |
        echo "Configuring systemd services..."
        timeout 120 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "
          # Install API service
          if [ ! -f /etc/systemd/system/domainmate-api.service ]; then
            sudo mv ~/domainmate-api.service /etc/systemd/system/
            sudo systemctl daemon-reload
            sudo systemctl enable domainmate-api.service
          else
            sudo cp ~/domainmate-api.service /etc/systemd/system/
            sudo systemctl daemon-reload
          fi
          
          # Install static service
          if [ ! -f /etc/systemd/system/domainmate-static.service ]; then
            sudo mv ~/domainmate-static.service /etc/systemd/system/
            sudo systemctl daemon-reload
            sudo systemctl enable domainmate-static.service
          else
            sudo cp ~/domainmate-static.service /etc/systemd/system/
            sudo systemctl daemon-reload
          fi
          
          # Remove old service if it exists
          if [ -f /etc/systemd/system/domainmate.service ]; then
            sudo systemctl stop domainmate.service || true
            sudo systemctl disable domainmate.service || true
            sudo rm /etc/systemd/system/domainmate.service
            sudo systemctl daemon-reload
          fi
        "
        echo "Systemd services configured."

    - name: Restart services
      run: |
        echo "Restarting services..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "
          # Start API server first
          sudo systemctl restart domainmate-api.service
          sleep 5
          # Then start static server
          sudo systemctl restart domainmate-static.service
        "
        echo "Services restarted."

    - name: Check services status
      run: |
        echo "Checking services status..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "
          echo '=== API Server Status ==='
          sudo systemctl status domainmate-api.service --no-pager
          echo '=== Static Server Status ==='
          sudo systemctl status domainmate-static.service --no-pager
        "
        echo "Status check completed."

    - name: Clean up temporary files
      run: |
        echo "Cleaning up temporary files..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "rm ~/domainmate-deploy.tar.gz ~/domainmate-api.service ~/domainmate-static.service"
        echo "Cleanup completed."