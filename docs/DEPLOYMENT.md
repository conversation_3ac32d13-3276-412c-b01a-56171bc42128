# Deployment Guide

DomainMate uses a **hybrid architecture** with Static Site Generation (SSG) for the frontend and a separate API server for backend functionality.

## Architecture Overview

- **Static File Server** (Port 3000): Serves pre-built HTML/CSS/JS files and proxies API requests
- **API Server** (Port 3001): Handles AI domain generation, database operations, and authentication
- **Database**: PostgreSQL (can be hosted separately)

## Deployment Architecture

### Production Setup
```
┌─────────────────┐    ┌─────────────────┐
│  Static Server  │    │   API Server    │
│   (Port 3000)   │───▶│   (Port 3001)   │
│                 │    │                 │
│ • Serves React  │    │ • OpenAI/Gemini │
│ • Proxies /api  │    │ • Database      │
│ • Client routing│    │ • Auth          │
└─────────────────┘    └─────────────────┘
```

### What Gets Deployed
1. **Static Files** (`out/` directory) - Pre-built React app
2. **API Server** (`dist/` directory) - Compiled Express.js server
3. **Static File Server** - Simple Express.js proxy server
4. **Database Migrations** - Automatic schema updates
5. **Systemd Services** - Process management for both servers

## GitHub Actions Deployment

### Required GitHub Secrets

Configure these secrets in your GitHub repository settings (`Settings > Secrets and variables > Actions`):

#### VPS Connection
- `SSH_PRIVATE_KEY` - Your SSH private key for VPS access
- `VPS_IP` - IP address of your VPS
- `VPS_USERNAME` - Username on your VPS (usually `root`)

#### Database
- `DATABASE_URL` - PostgreSQL connection string
  ```
  postgresql://username:password@host:port/database
  ```

#### AI Services
- `OPENAI_API_KEY` - Your OpenAI API key
- `GEMINI_API_KEY` - Your Google Gemini API key  
- `AI_PROVIDER` - Which AI service to use (`openai` or `gemini`)

#### Authentication (Auth0)
- `AUTH0_DOMAIN` - Your Auth0 domain (e.g., `your-app.auth0.com`)
- `AUTH0_AUDIENCE` - Your Auth0 API identifier
- `NEXT_PUBLIC_AUTH0_DOMAIN` - Same as AUTH0_DOMAIN (for client-side)
- `NEXT_PUBLIC_AUTH0_CLIENT_ID` - Your Auth0 application client ID
- `NEXT_PUBLIC_AUTH0_AUDIENCE` - Same as AUTH0_AUDIENCE (for client-side)

### Deployment Process

The GitHub Actions workflow automatically:

1. **Builds both applications**:
   - Static site (`npm run build`) → `out/` directory
   - API server (`npm run build:server`) → `dist/` directory

2. **Creates deployment package**:
   - Static files + Static file server + API server + Dependencies

3. **Deploys to VPS**:
   - Transfers files via SSH
   - Installs dependencies
   - Runs database migrations
   - Configures systemd services
   - Starts both servers

4. **Service Management**:
   - `domainmate-api.service` - API server on port 3001
   - `domainmate-static.service` - Static server on port 3000

## Manual Deployment

For manual deployment, use the updated script:

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh <vps_username> <vps_ip>
```

**Note**: You'll need to set environment variables manually on the server after deployment.

## Environment Variables

### Server Environment Variables
Set these on your VPS (not in GitHub secrets):

```bash
# Create /etc/environment or use systemd service files
DATABASE_URL=postgresql://username:password@host:port/database
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
AI_PROVIDER=openai
AUTH0_DOMAIN=your-app.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier
NODE_ENV=production
```

### Client Environment Variables
These are set at build time and embedded in the static files:

```bash
NEXT_PUBLIC_SITE_URL=https://domainmate.net
NEXT_PUBLIC_AUTH0_DOMAIN=your-app.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-client-id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
```

## Service Management

### Systemd Services

Two services are created:

1. **API Server** (`domainmate-api.service`)
2. **Static Server** (`domainmate-static.service`)

### Common Commands

```bash
# Check service status
sudo systemctl status domainmate-api.service
sudo systemctl status domainmate-static.service

# View logs
sudo journalctl -u domainmate-api.service -f
sudo journalctl -u domainmate-static.service -f

# Restart services
sudo systemctl restart domainmate-api.service
sudo systemctl restart domainmate-static.service

# Stop services
sudo systemctl stop domainmate-api.service
sudo systemctl stop domainmate-static.service
```

## Troubleshooting

### Common Issues

1. **API calls failing**: Check if API server is running on port 3001
2. **Static site not loading**: Check if static server is running on port 3000
3. **Database errors**: Verify DATABASE_URL and run migrations
4. **AI generation failing**: Check API keys and AI_PROVIDER setting

### Debugging Steps

1. **Check service status**:
   ```bash
   sudo systemctl status domainmate-api.service
   sudo systemctl status domainmate-static.service
   ```

2. **View logs**:
   ```bash
   sudo journalctl -u domainmate-api.service -n 50
   sudo journalctl -u domainmate-static.service -n 50
   ```

3. **Test API server directly**:
   ```bash
   curl http://localhost:3001/api/domains/tlds
   ```

4. **Test static server**:
   ```bash
   curl http://localhost:3000
   ```

### Port Configuration

- **Port 3000**: Static file server (public-facing)
- **Port 3001**: API server (internal, proxied by static server)

Make sure your firewall/reverse proxy only exposes port 3000 to the internet.

## Performance Optimization

- Static files are pre-built and optimized by Next.js
- API server handles only dynamic requests
- Database connection pooling is configured
- Rate limiting prevents abuse
- Proper caching headers are set

## Security

- API server is not directly exposed to internet
- All API requests go through the static server proxy
- Environment variables are properly secured
- CORS and security headers are configured
- Authentication is handled by Auth0

## Monitoring

Monitor both services:
- Static server: File serving and proxy functionality
- API server: AI generation, database operations, authentication

Set up log rotation and monitoring alerts for production use.
