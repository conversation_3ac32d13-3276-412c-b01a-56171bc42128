/**
 * Information about a domain name including availability status
 */
export interface DomainInfo {
  name: string;              // Domain name without TLD (e.g., "example")
  tld: string;               // Top-level domain (e.g., "com")
  fullDomain: string;        // Complete domain name (e.g., "example.com")
  available: boolean;        // Whether the domain is available for registration
  price?: number | string | null;  // Registration price if available
  registrationDate?: string; // When the domain was registered (if not available)
  type?: string;             // Additional information about the domain status
  timestamp?: string;        // When this domain was last checked
}

/**
 * Represents a group of domains with the same base name but different TLDs
 */
export interface GroupedDomainInfo {
  baseName: string;          // Domain name without TLD
  domains: DomainInfo[];     // All domain variations with different TLDs
  availableTlds: string[];   // List of TLDs that are available
  unavailableTlds: string[]; // List of TLDs that are not available
  lowestPrice?: number | string | null; // Lowest price among available domains
  type?: string;             // Additional information about the domain group
}

/**
 * Parameters for domain search requests
 */
export interface DomainSearchParams {
  term: string;              // Search term to generate domains for
  tlds?: string[];           // Specific TLDs to check
  limit?: number;            // Maximum number of domains to generate
}

/**
 * Response format for domain search API
 */
export interface DomainSearchResponse {
  searchTerm: string;        // Original search term
  domains: DomainInfo[];     // List of domain information
  timestamp: string;         // When the search was performed
}

/**
 * Information about rate limiting status
 */
export interface RateLimitInfo {
  remaining: number;         // Number of requests remaining in the current period
  total: number;             // Total number of requests allowed per period
  resetTime: string;         // When the rate limit will reset
  isLimited: boolean;        // Whether the user is currently rate limited
}

/**
 * Response format when a request is rate limited
 */
export interface RateLimitedResponse {
  rateLimited: true;         // Indicates this is a rate limit response
  rateLimitInfo: RateLimitInfo; // Rate limit details
  message: string;           // Human-readable message about the rate limit
}

/**
 * Response format for domain query API
 */
export interface DomainQueryData {
  domains: DomainInfo[];     // List of domain information
  searchTerm?: string;       // Original search term (optional)
  timestamp?: string;        // When the query was performed (optional)
}
