# Testing DomainMate Deployment on Linux/Ubuntu

This guide shows you how to test your DomainMate deployment locally on Linux/Ubuntu before pushing to production.

## 🛠️ Prerequisites

Make sure you have these installed:
- **Node.js** (v18 or higher): `sudo apt update && sudo apt install nodejs npm`
- **npm** (comes with Node.js)
- **Git**: `sudo apt install git`
- **curl** (for testing): `sudo apt install curl`

## 🧪 Testing Options

### **Option 1: Quick Build Test (Recommended First Step)**

Test just the build process without running servers:

```bash
./scripts/test-build-only.sh
```

Or test builds manually:
```bash
npm run build
npm run build:server
```

### **Option 2: Full Local Deployment Test**

Test the complete deployment setup with both servers running:

```bash
./scripts/test-deployment-locally.sh
```

This will:
1. Build both static site and API server
2. Create a test deployment package
3. Start both servers (API on port 3001, Static on port 3000)
4. Show you test URLs and commands

## 🔧 Setting Up Environment Variables

Before testing, set up your environment variables. You have several options:

### **Option A: Create .env file (Recommended)**
Create a `.env` file in your project root:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/domainmate
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
AI_PROVIDER=openai
AUTH0_DOMAIN=your-app.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier
NEXT_PUBLIC_AUTH0_DOMAIN=your-app.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-client-id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
```

### **Option B: Export Environment Variables**
```bash
export DATABASE_URL="postgresql://username:password@localhost:5432/domainmate"
export OPENAI_API_KEY="your_openai_api_key"
export GEMINI_API_KEY="your_gemini_api_key"
export AI_PROVIDER="openai"
export AUTH0_DOMAIN="your-app.auth0.com"
export AUTH0_AUDIENCE="https://your-api-identifier"
export NEXT_PUBLIC_AUTH0_DOMAIN="your-app.auth0.com"
export NEXT_PUBLIC_AUTH0_CLIENT_ID="your-client-id"
export NEXT_PUBLIC_AUTH0_AUDIENCE="https://your-api-identifier"
```

### **Option C: Create a .env.local file**
```bash
# Copy example and edit
cp .env.example .env.local
nano .env.local  # or use your preferred editor
```

## 🧪 Testing Steps

### **Step 1: Test Build Process**

First, make sure both builds work:

```bash
./scripts/test-build-only.sh
```

Expected output:
```
🔨 Testing DomainMate Build Process
===================================
🧹 Cleaning previous builds...
🔨 Testing static site build...
✅ Static site build successful
   Generated files in: out/
   Files count: 45
🔨 Testing API server build...
✅ API server build successful
   Generated files in: dist/
   Main file: dist/index.js (126K)
🔍 Verifying build outputs...
✅ Static site index.html exists
✅ Next.js assets directory exists
✅ API server bundle exists
✅ API server bundle is valid JavaScript

🎉 All builds successful!
```

### **Step 2: Test Local Deployment**

If builds work, test the full deployment:

```bash
./scripts/test-deployment-locally.sh
```

This will start both servers and show you:
```
✅ Both servers are running!

🌐 Application URLs:
   Frontend: http://localhost:3000
   API:      http://localhost:3001

🧪 Test Commands (run in another terminal):
   curl http://localhost:3000                    # Test static server
   curl http://localhost:3001/api/domains/tlds   # Test API server directly
   curl http://localhost:3000/api/domains/tlds   # Test API through proxy
```

### **Step 3: Test the Application**

Open your browser and test:

1. **Frontend**: http://localhost:3000
   - Should show the DomainMate homepage
   - Try searching for domains
   - Check if the UI loads correctly

2. **API Direct**: http://localhost:3001/api/domains/tlds
   - Should return JSON with available TLDs
   - Tests if API server is working

3. **API via Proxy**: http://localhost:3000/api/domains/tlds
   - Should return the same JSON
   - Tests if static server proxy is working

### **Step 4: Test API Endpoints**

Use curl to test API endpoints:

```bash
# Test TLDs endpoint
curl http://localhost:3000/api/domains/tlds

# Test domain search (if you have API keys set up)
curl "http://localhost:3000/api/domains/search?term=example&tlds=com,net"

# Test with pretty JSON output
curl -s http://localhost:3000/api/domains/tlds | jq .
```

Or use wget if you prefer:
```bash
wget -qO- http://localhost:3000/api/domains/tlds
```

## 🐛 Troubleshooting

### **Build Failures**

If builds fail, check:
1. **Node.js version**: `node --version` (should be v18+)
2. **Dependencies**: `npm install`
3. **TypeScript errors**: `npm run check`
4. **Permissions**: Make sure scripts are executable: `chmod +x scripts/*.sh`

### **Server Start Failures**

If servers won't start:
1. **Port conflicts**: Check if ports 3000 and 3001 are free:
   ```bash
   sudo netstat -tlnp | grep :3000
   sudo netstat -tlnp | grep :3001
   ```
2. **Environment variables**: Check if required vars are set: `env | grep -E "(DATABASE_URL|API_KEY|AUTH0)"`
3. **Dependencies**: Make sure `npm install` completed successfully
4. **Permissions**: Check file permissions in the project directory

### **API Errors**

If API calls fail:
1. **Database**: Check if DATABASE_URL is correct and database is accessible
2. **API Keys**: Verify OPENAI_API_KEY or GEMINI_API_KEY are valid
3. **Network**: Check if both servers are running: `ps aux | grep node`
4. **Firewall**: Check if UFW is blocking ports:
   ```bash
   sudo ufw status
   # If needed, allow ports:
   sudo ufw allow 3000
   sudo ufw allow 3001
   ```

### **Common Linux Issues**

1. **Permission Denied**: Make scripts executable:
   ```bash
   chmod +x scripts/*.sh
   ```

2. **Port Already in Use**: Kill existing processes:
   ```bash
   sudo lsof -ti:3000 | xargs sudo kill -9
   sudo lsof -ti:3001 | xargs sudo kill -9
   ```

3. **Node.js Version**: Update Node.js if needed:
   ```bash
   # Using NodeSource repository
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

## 🚀 Next Steps

Once local testing works:

### **Deploy via GitHub Actions (Recommended)**
1. Set up GitHub Secrets (see [GITHUB_SECRETS_SETUP.md](GITHUB_SECRETS_SETUP.md))
2. Push to main branch: `git push origin main`
3. Monitor deployment in GitHub Actions tab

### **Manual Deployment**
```bash
./scripts/deploy.sh <username> <ip>
```

## 📊 Performance Testing

For performance testing on Linux:

```bash
# Test static file serving speed
time curl -s http://localhost:3000 > /dev/null

# Test API response time
time curl -s http://localhost:3000/api/domains/tlds > /dev/null

# Load testing with Apache Bench (if installed)
ab -n 100 -c 10 http://localhost:3000/
```

## 🔍 Monitoring

While testing, monitor:
- **System resources**: `htop` or `top` to check CPU/Memory usage
- **Network connections**: `netstat -tlnp` to see listening ports
- **Process status**: `ps aux | grep node` to see Node.js processes
- **Logs**: Check terminal output for error messages

## 📝 Testing Checklist

Before deploying to production, verify:

- [ ] Both builds complete successfully
- [ ] Static server serves files on port 3000
- [ ] API server responds on port 3001
- [ ] Proxy routing works (API calls through static server)
- [ ] Frontend loads and displays correctly
- [ ] Domain search functionality works (if API keys configured)
- [ ] No console errors in browser
- [ ] Environment variables are properly configured
- [ ] Scripts are executable (`chmod +x scripts/*.sh`)

## 🆘 Getting Help

If you encounter issues:

1. **Check logs**: Look for error messages in terminal output
2. **Verify setup**: Ensure all prerequisites are installed
3. **Environment**: Double-check environment variables with `env | grep -E "(DATABASE|API|AUTH)"`
4. **Permissions**: Verify file and script permissions
5. **GitHub Issues**: Create an issue with error details and system info:
   ```bash
   # Include this info in your issue:
   node --version
   npm --version
   uname -a
   ```
6. **Documentation**: Review [DEPLOYMENT.md](DEPLOYMENT.md) for more details

## 🔧 Useful Commands

```bash
# Check if services are running
ps aux | grep node

# Check port usage
sudo netstat -tlnp | grep -E ":(3000|3001)"

# Kill all Node.js processes (if needed)
pkill -f node

# Check system resources
htop

# View real-time logs
tail -f /var/log/syslog | grep node

# Test network connectivity
curl -I http://localhost:3000
curl -I http://localhost:3001
``` 