import { useAuth } from './use-auth';
import { apiFetch, parseApiResponse } from '@/lib/api-helpers';
import { useCallback } from 'react';

/**
 * Custom hook that combines Auth0 authentication with API fetch
 * This hook provides authenticated API calls with the current user's token
 */
export function useAuthFetch() {
  const { getToken } = useAuth();

  /**
   * Fetch data from an API endpoint with authentication
   */
  const authFetch = useCallback(
    async <T>(url: string, options: RequestInit = {}): Promise<T> => {
      try {
        // Get the auth token
        const token = await getToken();

        // Make the API request with the token
        const response = await apiFetch(url, options, token);

        // Parse the response
        return parseApiResponse<T>(response);
      } catch (error) {
        throw error;
      }
    },
    [getToken]
  );

  return { authFetch };
}
