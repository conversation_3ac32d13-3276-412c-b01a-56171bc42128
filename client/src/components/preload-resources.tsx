import { useEffect, useRef } from 'react';

/**
 * Component that adds preload hints to improve FCP and LCP performance
 * This helps the browser prioritize critical resources
 */
export function PreloadResources() {
  // Keep track of elements we add to the DOM for cleanup
  const addedElements = useRef<HTMLElement[]>([]);

  useEffect(() => {
    // Only run in the browser
    if (typeof document === 'undefined') return;

    const elements: HTMLElement[] = [];

    // Function to create and append elements
    const createAndAppend = (tagName: string, attributes: Record<string, string>) => {
      const element = document.createElement(tagName);
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
      document.head.appendChild(element);
      elements.push(element);
      return element;
    };

    // Preload critical CSS (handled by Next.js automatically)
    // No need to manually preload CSS in SSG mode

    // Font preconnect and preload
    createAndAppend('link', {
      rel: 'preconnect',
      href: 'https://fonts.googleapis.com'
    });

    createAndAppend('link', {
      rel: 'preconnect',
      href: 'https://fonts.gstatic.com',
      crossorigin: ''
    });

    // Preload critical font files
    createAndAppend('link', {
      rel: 'preload',
      as: 'font',
      href: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2',
      type: 'font/woff2',
      crossorigin: ''
    });

    // Add resource hints for key third-party domains
    createAndAppend('link', {
      rel: 'dns-prefetch',
      href: 'https://www.googletagmanager.com'
    });

    // Store elements for cleanup
    addedElements.current = elements;

    // Clean up function
    return () => {
      addedElements.current.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
    };
  }, []);

  return null; // This component doesn't render anything
}
