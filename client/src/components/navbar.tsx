import { BookOpen } from "lucide-react";
import { UserProfile } from "./user-profile";
import Link from "next/link";

export function Navbar() {
  return (
    <div className="relative z-20 px-4 sm:px-6">
      <nav className="flex justify-between items-center py-4 mb-8 max-w-full overflow-visible">
        <a href="/" className="flex items-center gap-2 group shrink-0">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl overflow-hidden shadow-lg transition-all duration-300 group-hover:shadow-primary-500/25 group-hover:rotate-3">
            <img src="/logo.png" alt="DomainMate Logo" className="w-full h-full object-cover" />
          </div>
          <div>
            <span className="text-xl sm:text-2xl font-bold text-gradient">
              DomainMate
            </span>
          </div>
        </a>

        <div className="flex items-center gap-3 sm:gap-6 ml-2">
          <Link href="/blog" className="flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1.5 rounded-lg bg-gradient-to-r from-primary-50 to-secondary-50 border border-primary-100 text-primary-700 hover:from-primary-100 hover:to-secondary-100 hover:text-primary-800 hover:shadow-sm transition-all duration-300 group shrink-0">
            <BookOpen className="w-4 h-4 sm:w-5 sm:h-5 group-hover:rotate-3 transition-transform duration-300" />
            <span className="font-medium hidden md:inline">DomainMate Blog</span>
            <span className="font-medium md:hidden">Blog</span>
          </Link>
          <UserProfile />
        </div>
      </nav>
    </div>
  );
}
