// Auth0 configuration
// Support Next.js environment variables
const getEnvVar = (nextKey: string, fallback: string) => {
  if (typeof window !== 'undefined') {
    // Client-side: check window.ENV first, then process.env, then fallback
    return (window as any).ENV?.[nextKey.replace('NEXT_PUBLIC_', '')] ||
           process.env[nextKey] ||
           fallback;
  }
  // Server-side: use process.env
  return process.env[nextKey] || fallback;
};

const domain = getEnvVar('NEXT_PUBLIC_AUTH0_DOMAIN', 'auth.domainmate.net');
const clientId = getEnvVar('NEXT_PUBLIC_AUTH0_CLIENT_ID', 'kCkykBJw9agqnP1Nl1ImKxQgVyA9T0uS');
const audience = getEnvVar('NEXT_PUBLIC_AUTH0_AUDIENCE', 'https://api.domainmate.net');

// Auth0 configuration is loaded from environment variables
export const auth0Config = {
  domain,
  clientId,
  authorizationParams: {
    redirect_uri: typeof window !== 'undefined' ? window.location.origin : 'https://domainmate.net',
    audience,
  },
  cacheLocation: 'localstorage' as const,
};
