import Head from 'next/head';
import Link from 'next/link';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Home, Search } from 'lucide-react';

export default function Custom404() {
  return (
    <>
      <Head>
        <title>Page Not Found | DomainMate</title>
        <meta 
          name="description" 
          content="The page you're looking for doesn't exist. Return to DomainMate to find the perfect domain name for your business." 
        />
        <meta name="robots" content="noindex, nofollow" />
        
        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/404" />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="flex items-center justify-center min-h-[60vh]">
              <Card className="max-w-md w-full">
                <CardContent className="p-8 text-center">
                  <div className="mb-6">
                    <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">
                      Page Not Found
                    </h1>
                    <p className="text-gray-600">
                      The page you're looking for doesn't exist or has been moved.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <Link href="/">
                      <Button className="w-full">
                        <Home className="w-4 h-4 mr-2" />
                        Go to Homepage
                      </Button>
                    </Link>
                    
                    <Link href="/">
                      <Button variant="outline" className="w-full">
                        <Search className="w-4 h-4 mr-2" />
                        Search for Domains
                      </Button>
                    </Link>
                  </div>

                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                      Looking for something specific? Try our{' '}
                      <Link href="/" className="text-blue-600 hover:text-blue-800">
                        domain name generator
                      </Link>{' '}
                      to find the perfect domain for your business.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}
