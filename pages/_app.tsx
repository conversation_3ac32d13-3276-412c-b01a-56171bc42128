import type { AppProps } from 'next/app';
import Head from 'next/head';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { Toaster } from '@/components/ui/toaster';
import { FavoritesProvider } from '@/components/favorites-provider';
import { Auth0ProviderWithNavigate } from '@/components/auth0-provider';
import { PreloadResources } from '@/components/preload-resources';
import '../styles/globals.css';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        {/* Default meta tags - can be overridden by individual pages */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="author" content="DomainMate" />

        {/* Favicon */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/site.webmanifest" />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Load fonts with font-display:swap */}
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
          media="print"
          onLoad={(e) => { (e.target as HTMLLinkElement).media = 'all'; }}
        />

        {/* Critical CSS */}
        <style dangerouslySetInnerHTML={{
          __html: `
            body, html {
              margin: 0;
              padding: 0;
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #__next {
              min-height: 100vh;
            }
            .initial-content {
              font-family: 'Inter', sans-serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              text-align: center;
            }
            .initial-heading {
              font-size: 2rem;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 1rem;
            }
            .initial-text {
              font-size: 1.125rem;
              color: #4b5563;
              max-width: 600px;
              margin: 0 auto;
            }
          `
        }} />

        {/* Google AdSense */}
        <meta name="google-adsense-account" content="ca-pub-****************" />
      </Head>

      <Auth0ProviderWithNavigate>
        <QueryClientProvider client={queryClient}>
          <FavoritesProvider>
            <PreloadResources />
            <Component {...pageProps} />
            <Toaster />
          </FavoritesProvider>
        </QueryClientProvider>
      </Auth0ProviderWithNavigate>
    </>
  );
}
