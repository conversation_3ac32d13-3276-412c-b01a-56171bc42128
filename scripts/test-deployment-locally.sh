#!/bin/bash

# Local Deployment Testing Script for Linux/Ubuntu
# This script simulates the production deployment locally

set -e

echo "🧪 Testing DomainMate Deployment Locally"
echo "========================================"

# Check if required environment variables are set
echo "📋 Checking environment variables..."
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️  Warning: DATABASE_URL not set"
fi

if [ -z "$OPENAI_API_KEY" ] && [ -z "$GEMINI_API_KEY" ]; then
    echo "⚠️  Warning: No AI API keys set (OPENAI_API_KEY or GEMINI_API_KEY)"
fi

if [ -z "$AI_PROVIDER" ]; then
    echo "⚠️  Warning: AI_PROVIDER not set, defaulting to 'openai'"
    export AI_PROVIDER="openai"
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf out dist test-deployment

# Build both applications
echo "🔨 Building static site..."
npm run build

echo "🔨 Building API server..."
npm run build:server

# Create test deployment directory
echo "📦 Creating test deployment package..."
mkdir -p test-deployment
cp -r out test-deployment/
cp -r dist test-deployment/
cp package.json package-lock.json test-deployment/

# Create static file server (same as production)
mkdir -p test-deployment/server-static

cat > test-deployment/server-static/server.js << 'EOF'
const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();
const port = process.env.PORT || 3000;

console.log('🌐 Static server starting...');

// Proxy API requests to the API server
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:3001',
  changeOrigin: true,
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error('❌ Proxy error:', err.message);
    res.status(500).json({ message: 'API server unavailable' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔄 Proxying ${req.method} ${req.url} to API server`);
  }
}));

// Serve static files from out directory
app.use(express.static(path.join(__dirname, '..', 'out')));

// Handle client-side routing - serve index.html for all routes
app.get('*', (req, res) => {
  console.log(`📄 Serving ${req.url}`);
  res.sendFile(path.join(__dirname, '..', 'out', 'index.html'));
});

app.listen(port, () => {
  console.log(`✅ Static server running on http://localhost:${port}`);
});
EOF

cat > test-deployment/server-static/package.json << 'EOF'
{
  "name": "domainmate-static-server-test",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.21.2",
    "http-proxy-middleware": "^2.0.6"
  }
}
EOF

# Install dependencies for static server
echo "📦 Installing static server dependencies..."
cd test-deployment/server-static
npm install --silent
cd ../..

# Install main dependencies
echo "📦 Installing main dependencies..."
cd test-deployment
npm install --production --silent
cd ..

echo ""
echo "🚀 Starting test deployment..."
echo "================================"
echo ""

# Function to cleanup processes
cleanup() {
    echo ""
    echo "🛑 Stopping test servers..."
    if [ ! -z "$API_PID" ] && kill -0 $API_PID 2>/dev/null; then
        kill $API_PID
        echo "   ✅ API server stopped"
    fi
    
    if [ ! -z "$STATIC_PID" ] && kill -0 $STATIC_PID 2>/dev/null; then
        kill $STATIC_PID
        echo "   ✅ Static server stopped"
    fi
    
    echo "✅ Cleanup complete"
    exit 0
}

# Set up cleanup on script exit
trap cleanup EXIT INT TERM

# Start API server in background
echo "🔧 Starting API server on port 3001..."
cd test-deployment
export NODE_ENV=production
export PORT=3001
node dist/index.js &
API_PID=$!
cd ..

# Wait a moment for API server to start
sleep 3

# Check if API server is running
if ! kill -0 $API_PID 2>/dev/null; then
    echo "❌ API server failed to start"
    exit 1
fi

# Start static server in background
echo "🌐 Starting static server on port 3000..."
cd test-deployment/server-static
export PORT=3000
node server.js &
STATIC_PID=$!
cd ../..

# Wait a moment for static server to start
sleep 2

# Check if static server is running
if ! kill -0 $STATIC_PID 2>/dev/null; then
    echo "❌ Static server failed to start"
    kill $API_PID 2>/dev/null
    exit 1
fi

echo ""
echo "✅ Both servers are running!"
echo ""
echo "🌐 Application URLs:"
echo "   Frontend: http://localhost:3000"
echo "   API:      http://localhost:3001"
echo ""
echo "🧪 Test Commands (run in another terminal):"
echo "   curl http://localhost:3000                    # Test static server"
echo "   curl http://localhost:3001/api/domains/tlds   # Test API server directly"
echo "   curl http://localhost:3000/api/domains/tlds   # Test API through proxy"
echo ""
echo "📊 Process Information:"
echo "   API Server PID: $API_PID"
echo "   Static Server PID: $STATIC_PID"
echo ""
echo "Press Ctrl+C to stop all servers and cleanup"
echo ""

# Keep script running and monitor processes
while true; do
    sleep 1
    
    # Check if processes are still running
    if ! kill -0 $API_PID 2>/dev/null; then
        echo "❌ API server stopped unexpectedly"
        break
    fi
    if ! kill -0 $STATIC_PID 2>/dev/null; then
        echo "❌ Static server stopped unexpectedly"
        break
    fi
done 