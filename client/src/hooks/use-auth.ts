import { useAuth0 } from '@auth0/auth0-react';
import { useCallback } from 'react';

export function useAuth() {
  const {
    isAuthenticated,
    isLoading,
    user,
    loginWithRedirect,
    logout,
    getAccessTokenSilently,
    getAccessTokenWithPopup,
  } = useAuth0();

  const login = useCallback(() => {
    loginWithRedirect();
  }, [loginWithRedirect]);

  const logoutUser = useCallback(() => {
    logout({
      logoutParams: {
        returnTo: window.location.origin
      }
    });
  }, [logout]);

  const getToken = useCallback(async () => {
    try {
      if (!isAuthenticated) {
        return null;
      }

      // Ensure we have a valid audience
      const audience = (window as any).ENV?.AUTH0_AUDIENCE ||
                      process.env.NEXT_PUBLIC_AUTH0_AUDIENCE ||
                      'https://api.domainmate.net';

      try {
        const token = await getAccessTokenSilently({
          authorizationParams: {
            audience: audience,
          },
          detailedResponse: false
        });

        // If we get a detailed response, extract the access token
        if (token && typeof token === 'object' && 'access_token' in (token as any)) {
          return (token as any).access_token;
        }

        return token;
      } catch (tokenError) {
        // Try again with a different approach
        const popupToken = await getAccessTokenWithPopup({
          authorizationParams: {
            audience: audience,
          }
        });

        return popupToken;
      }
    } catch (error) {
      return null;
    }
  }, [getAccessTokenSilently, getAccessTokenWithPopup, isAuthenticated]);

  return {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout: logoutUser,
    getToken,
  };
}
