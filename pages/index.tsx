import Head from 'next/head';
import { GetStaticProps } from 'next';
import { Card, CardContent } from "@/components/ui/card";
import { DomainSearch } from "@/components/domain-search";
import { DomainGrid } from "@/components/domain-grid";
import { TldFilter } from "@/components/tld-filter";
import { FaqSection } from "@/components/faq-section";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { But<PERSON> } from "@/components/ui/button";
import { Search, ChevronUp, ListChecks } from "lucide-react";
import { DomainProgressTracker } from "@/components/domain-progress-tracker";
import { useDomainSearch } from "@/hooks/use-domain-search";
import { useState } from 'react';
import { RateLimitIndicator } from "@/components/rate-limit-indicator";
import { StandaloneFavoritesSection } from "@/components/standalone-favorites-section";
import { CustomDomainInput } from "@/components/custom-domain-input";
import { CustomDomainResults } from "@/components/custom-domain-results";
import { useCustomDomainCheck } from "@/hooks/use-custom-domain-check";
import { BulkDomainChecker } from "@/components/bulk-domain-checker";

interface HomePageProps {
  // Add any static props here if needed
}

export default function HomePage({}: HomePageProps) {
  const {
    searchTerm,
    searchDomains,
    isLoading: isSearchLoading,
    domains,
    groupedDomains,
    hasMadeSearch,
    popularTlds,
    selectedTlds,
    setSelectedTlds,
    searchStatus,
    refetch,
    generateMoreSuggestions
  } = useDomainSearch();

  // Custom domain check functionality
  const {
    domainName,
    checkDomainAvailability,
    isLoading: isCustomCheckLoading,
    availableDomains,
    unavailableDomains,
    hasChecked,
  } = useCustomDomainCheck(selectedTlds);

  // Combined loading state
  const isLoading = isSearchLoading || isCustomCheckLoading;

  // State to toggle custom domain input visibility
  const [showCustomDomainInput, setShowCustomDomainInput] = useState(false);

  // State to toggle between single and bulk domain search modes
  const [searchMode, setSearchMode] = useState<"single" | "bulk">("single");

  // When TLDs change, refetch results if there's already a search term
  const handleTldChange = (newTlds: string[]) => {
    setSelectedTlds(newTlds);

    // Trigger a new search if we've already made a search and have a search term
    if (hasMadeSearch && searchTerm) {
      // Short delay to ensure state is updated
      setTimeout(() => {
        refetch();
      }, 100);
    }
  };

  // Handle domain search and hide custom domain input when starting a new search
  const handleDomainSearch = (term: string) => {
    // Hide custom domain input when starting a new search
    setShowCustomDomainInput(false);
    // Perform the search
    searchDomains(term);
  };

  return (
    <>
      <Head>
        <title>Domain Name Generator & Business Name Maker | DomainMate</title>
        <meta
          name="description"
          content="Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."
        />
        <meta
          name="keywords"
          content="domain name generator, domain name maker, business name generator for free, business name generator ai, ai domain generator, domain availability checker, brand name ideas, domain suggestions, available domains"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="Domain Name Generator & Business Name Maker | DomainMate" />
        <meta property="og:description" content="Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly." />
        <meta property="og:url" content="https://domainmate.net" />
        <meta property="og:type" content="website" />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Domain Name Generator & Business Name Maker | DomainMate" />
        <meta name="twitter:description" content="Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly." />

        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/" />

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Domain Name Generator & Business Name Maker | DomainMate",
              "url": "https://domainmate.net",
              "description": "Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly.",
              "applicationCategory": "BusinessApplication",
              "operatingSystem": "All",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "creator": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              }
            })
          }}
        />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="mb-6">
              <Card className="bg-white shadow-xl mb-8 border-0 overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-teal-500"></div>
                <CardContent className="p-6 sm:p-8">
                  <div className="text-center mb-8">
                    <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                      AI Domain Name Generator
                    </h1>
                    <h2 className="text-xl sm:text-2xl font-semibold text-gray-700 mb-3">
                      Find the perfect available domain name instantly
                    </h2>
                    {/* Add priority loading hint for LCP element */}
                    <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-2"
                       id="main-description"
                       style={{ contentVisibility: 'auto' }}>
                      Our advanced AI domain name generator creates highly creative, distinctive name suggestions that perfectly capture your business idea's essence.
                    </p>
                    <p className="text-md text-gray-600 max-w-3xl mx-auto">
                      We prioritize creativity and relevance, then verify domain availability in real-time so you'll only see domains you can register immediately.
                    </p>
                  </div>

                  <div className="border-t border-gray-100 pt-8">
                    <DomainSearch
                      onSearch={handleDomainSearch}
                      isLoading={isLoading}
                    />

                    {/* Custom domain check toggle button */}
                    <div className="mt-4 max-w-md mx-auto">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full flex items-center justify-center gap-2"
                        onClick={() => setShowCustomDomainInput(!showCustomDomainInput)}
                      >
                        {showCustomDomainInput ? (
                          <>
                            <ChevronUp className="w-4 h-4" />
                            <span>Hide custom domain check</span>
                          </>
                        ) : (
                          <>
                            <Search className="w-4 h-4" />
                            <span>Check domain availability or bulk check</span>
                          </>
                        )}
                      </Button>
                    </div>

                    {/* Custom domain input and results - conditionally rendered */}
                    {showCustomDomainInput && (
                      <>
                        <div className="mt-4 max-w-md mx-auto p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                          <CustomDomainInput
                            onCheck={checkDomainAvailability}
                            isLoading={isCustomCheckLoading}
                          />

                          {/* Bulk domain checker button */}
                          <div className="mt-4 pt-4 border-t border-gray-100">
                            <div className="flex items-center justify-between">
                              <div className="text-sm text-gray-600">
                                Want to check multiple domains at once?
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                                onClick={() => setSearchMode("bulk")}
                              >
                                <ListChecks className="h-4 w-4" />
                                <span>Bulk Check</span>
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Custom domain results */}
                        <div className="mt-4">
                          <CustomDomainResults
                            domainName={domainName}
                            availableDomains={availableDomains}
                            unavailableDomains={unavailableDomains}
                            isLoading={isCustomCheckLoading}
                            hasChecked={hasChecked}
                          />
                        </div>
                      </>
                    )}

                    {/* Bulk Domain Checker - only shown when in bulk mode */}
                    {searchMode === "bulk" && (
                      <div className="mt-6">
                        <div className="mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => setSearchMode("single")}
                          >
                            <Search className="h-4 w-4" />
                            <span>Back to Single Domain Search</span>
                          </Button>
                        </div>
                        <BulkDomainChecker selectedTlds={selectedTlds} />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="mt-4 max-w-md mx-auto">
                <TldFilter
                  tlds={popularTlds || []}
                  selectedTlds={selectedTlds}
                  onChange={handleTldChange}
                />

                {/* Rate limit indicator */}
                <div className="mt-4">
                  <RateLimitIndicator />
                </div>
              </div>

              {/* Standalone Favorites Section */}
              <div className="mt-6">
                <StandaloneFavoritesSection />
              </div>
            </div>

            <Card className="bg-white shadow-md mb-10">
              <CardContent className="p-6">
                {!hasMadeSearch && !isLoading && (
                  <div className="text-center py-10">
                    <Globe className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-lg text-gray-500">Enter your idea above to get domain suggestions</p>
                  </div>
                )}

                {isLoading && (
                  <div className="relative py-8 overflow-hidden bg-white rounded-lg">
                    {/* Main content with improved loading screen */}
                    <div className="relative z-10">
                      {/* Progress tracker component */}
                      <DomainProgressTracker
                        isLoading={isLoading}
                        searchStatus={searchStatus}
                        searchTerm={searchTerm}
                      />
                    </div>
                  </div>
                )}

                {hasMadeSearch && !isLoading && (
                  <div>
                    <div className="mb-6">
                      <h2 className="text-xl font-bold text-gray-800">
                        Domain suggestions for <span className="text-blue-600">{searchTerm}</span>
                      </h2>
                    </div>

                    <DomainGrid
                      domains={domains}
                      groupedDomains={groupedDomains}
                      isLoading={isLoading}
                      searchStatus={searchStatus}
                      onGenerateMore={generateMoreSuggestions}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <FaqSection />
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

function Globe(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={2}
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9"
      />
    </svg>
  );
}

// This function gets called at build time for static generation
export const getStaticProps: GetStaticProps<HomePageProps> = async () => {
  // You can fetch data here if needed for static generation
  // For now, we'll just return empty props since the page is mostly client-side

  return {
    props: {},
  };
};
