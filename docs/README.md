# DomainMate

DomainMate is an AI-powered domain name generator and availability checker that helps you find the perfect domain name for your business or project. Built with a modern hybrid architecture combining Next.js SSG for optimal SEO and performance with a dedicated API server for dynamic functionality.

## Features

- **AI-Powered Generation**: Uses advanced AI models (OpenAI GPT-4 and Google Gemini) to generate creative and relevant domain names
- **Real-time Domain Availability**: Checks domain availability across multiple TLDs
- **Bulk Domain Checker**: Check availability of up to 100 domains at once (requires authentication)
- **User Authentication**: Secure authentication via Auth0
- **Favorites System**: Save and manage favorite domain names
- **Blog System**: Static blog with SEO-optimized articles
- **SEO Optimized**: Static Site Generation with proper meta tags and structured data
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Architecture

DomainMate uses a **hybrid architecture** for optimal performance and functionality:

### Production Setup
```
┌─────────────────┐    ┌─────────────────┐
│  Static Server  │    │   API Server    │
│   (Port 3000)   │───▶│   (Port 3001)   │
│                 │    │                 │
│ • Serves React  │    │ • OpenAI/Gemini │
│ • Proxies /api  │    │ • Database      │
│ • Client routing│    │ • Auth          │
└─────────────────┘    └─────────────────┘
```

**Components:**
- **Static File Server**: Next.js 14 with static generation for optimal SEO and performance
- **API Server**: Express.js server for dynamic functionality (AI generation, authentication, database operations)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Auth0 for secure user management

## AI Provider Support

DomainMate supports multiple AI providers for domain name generation:

- **OpenAI**: Uses OpenAI's GPT models for creative domain name generation
- **Google Gemini**: Uses Google's Gemini models as an alternative

You can easily switch between these providers by changing the `AI_PROVIDER` environment variable.

## Quick Start

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd domainmate
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Create environment file**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database connection
   DATABASE_URL=postgres://username:password@localhost:5432/domainmate

   # AI Services
   OPENAI_API_KEY=your_openai_api_key
   GEMINI_API_KEY=your_gemini_api_key
   AI_PROVIDER=openai

   # Auth0 Configuration
   AUTH0_DOMAIN=your-app.auth0.com
   AUTH0_AUDIENCE=https://your-api-identifier
   NEXT_PUBLIC_AUTH0_DOMAIN=your-app.auth0.com
   NEXT_PUBLIC_AUTH0_CLIENT_ID=your-client-id
   NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
   ```

4. **Set up database**
   ```bash
   npm run db:push
   npm run db:apply-rls
   ```

5. **Start development servers**
   ```bash
   # Terminal 1: Start Next.js development server
   npm run dev

   # Terminal 2: Start API server (optional for API development)
   npm run dev:server
   ```

   The application will be available at `http://localhost:3000`

### Switching AI Providers

To switch between AI providers, simply change the `AI_PROVIDER` environment variable to either `openai` or `gemini`. The application will automatically use the corresponding API key and service.

## Building for Production

The build process generates both static files and the API server:

```bash
# Build static site (Next.js SSG)
npm run build

# Build API server (Express.js)
npm run build:server
```

This creates:
- **Static files** in the `out/` directory (pre-built React app)
- **API server** in the `dist/` directory (compiled Express.js server)

## 🧪 Testing Before Deployment

**Always test your deployment locally before pushing to production!**

### **Quick Build Test**
```bash
# Test builds only
./scripts/test-build-only.sh
```

### **Full Local Deployment Test**
```bash
# Test complete deployment setup
./scripts/test-deployment-locally.sh
```

### **Manual Testing**
```bash
# Test builds manually
npm run build
npm run build:server
```

📖 **Detailed Testing Guide**: See [TESTING_ON_LINUX.md](TESTING_ON_LINUX.md) for comprehensive Linux/Ubuntu testing instructions.

## Deployment

DomainMate's hybrid architecture provides flexible deployment options with automated CI/CD.

### 🚀 Automated Deployment (Recommended)

1. **Set up GitHub Secrets** - Follow the [GitHub Secrets Setup Guide](GITHUB_SECRETS_SETUP.md)
2. **Test locally first** - Use the testing scripts above
3. **Push to main branch** - Deployment happens automatically via GitHub Actions
4. **Monitor deployment** - Check the Actions tab for deployment status

### 📋 Manual Deployment

```bash
# Test first
./scripts/test-build-only.sh

# Then deploy
chmod +x scripts/deploy.sh
./scripts/deploy.sh <vps_username> <vps_ip>
```

### 📖 Detailed Documentation

- **[Deployment Guide](DEPLOYMENT.md)** - Complete deployment documentation
- **[GitHub Secrets Setup](GITHUB_SECRETS_SETUP.md)** - Step-by-step secrets configuration
- **[Testing on Linux](TESTING_ON_LINUX.md)** - Linux/Ubuntu testing guide
- **[Security Guidelines](SECURITY.md)** - Security best practices

## Environment Variables

### Required for Development
```env
DATABASE_URL=postgresql://username:password@host:port/database
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
AI_PROVIDER=openai
AUTH0_DOMAIN=your-app.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier
NEXT_PUBLIC_AUTH0_DOMAIN=your-app.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-client-id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
```

### Production Deployment
Environment variables are automatically configured via GitHub Secrets during deployment.

## Service Management

In production, two systemd services are created:

```bash
# Check service status
sudo systemctl status domainmate-api.service
sudo systemctl status domainmate-static.service

# View logs
sudo journalctl -u domainmate-api.service -f
sudo journalctl -u domainmate-static.service -f

# Restart services
sudo systemctl restart domainmate-api.service
sudo systemctl restart domainmate-static.service
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. **Test locally** using the testing scripts
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

---

**Need help?** Check out our documentation:
- 🧪 [Testing Guide](TESTING_ON_LINUX.md) - Test before deploying
- 🚀 [Deployment Guide](DEPLOYMENT.md) - Complete deployment docs
- 🔑 [GitHub Secrets](GITHUB_SECRETS_SETUP.md) - Set up automated deployment
- 🐛 [Create an Issue](../../issues) - Report problems or get help
