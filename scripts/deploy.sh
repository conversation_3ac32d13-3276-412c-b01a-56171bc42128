#!/bin/bash

# This script can be used for manual deployment
# Usage: ./scripts/deploy.sh <vps_username> <vps_ip>

if [ $# -ne 2 ]; then
  echo "Usage: $0 <vps_username> <vps_ip>"
  exit 1
fi

VPS_USERNAME=$1
VPS_IP=$2

# Build the application
echo "Building static site..."
npm run build

echo "Building API server..."
npm run build:server

# Create deployment package
echo "Creating deployment package..."
mkdir -p deployment
cp -r dist deployment/
cp -r out deployment/
cp -r migrations deployment/
cp -r scripts deployment/
cp package.json package-lock.json deployment/

# Create static file server
mkdir -p deployment/server-static
cat > deployment/server-static/server.js << 'EOF'
const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();
const port = process.env.PORT || 3000;

// Proxy API requests to the API server
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:3001',
  changeOrigin: true,
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ message: 'API server unavailable' });
  }
}));

// Serve static files from out directory
app.use(express.static(path.join(__dirname, '..', 'out')));

// Handle client-side routing - serve index.html for all routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '..', 'out', 'index.html'));
});

app.listen(port, () => {
  console.log(`Static server running on port ${port}`);
});
EOF

cat > deployment/server-static/package.json << 'EOF'
{
  "name": "domainmate-static-server",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.21.2",
    "http-proxy-middleware": "^2.0.6"
  }
}
EOF

# NOTE: .env file removed - environment variables should be set on the server
cd deployment && tar -czf ../domainmate-deploy.tar.gz .
cd ..

# Create systemd service files
echo "Creating systemd service files..."

# Static file server service
cat > domainmate-static.service << EOF
[Unit]
Description=DomainMate Static File Server
After=network.target domainmate-api.service
Requires=domainmate-api.service

[Service]
Type=simple
User=$VPS_USERNAME
WorkingDirectory=/home/<USER>/domainmate/server-static
ExecStart=/usr/bin/node /home/<USER>/domainmate/server-static/server.js
Restart=on-failure
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
EOF

# API server service
cat > domainmate-api.service << EOF
[Unit]
Description=DomainMate API Server
After=network.target

[Service]
Type=simple
User=$VPS_USERNAME
WorkingDirectory=/home/<USER>/domainmate
ExecStart=/usr/bin/node /home/<USER>/domainmate/dist/index.js
Restart=on-failure
Environment=NODE_ENV=production
Environment=PORT=3001

[Install]
WantedBy=multi-user.target
EOF

# Deploy to VPS
echo "Deploying to VPS..."
scp domainmate-deploy.tar.gz domainmate-static.service domainmate-api.service $VPS_USERNAME@$VPS_IP:~/
ssh $VPS_USERNAME@$VPS_IP << EOF
  mkdir -p ~/domainmate
  tar -xzf ~/domainmate-deploy.tar.gz -C ~/domainmate
  cd ~/domainmate
  
  # NOTE: You need to set these environment variables on the server:
  # DATABASE_URL, OPENAI_API_KEY, GEMINI_API_KEY, AI_PROVIDER, AUTH0_DOMAIN, AUTH0_AUDIENCE
  # Make sure to set these environment variables before running the application
  
  # Install main dependencies
  npm ci --production
  
  # Install static server dependencies
  cd server-static && npm install --production
  cd ..

  # Run database migrations and apply RLS policies
  npm run db:push:all

  # Install API service
  if [ ! -f /etc/systemd/system/domainmate-api.service ]; then
    sudo mv ~/domainmate-api.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable domainmate-api.service
  else
    sudo cp ~/domainmate-api.service /etc/systemd/system/
    sudo systemctl daemon-reload
  fi
  
  # Install static service
  if [ ! -f /etc/systemd/system/domainmate-static.service ]; then
    sudo mv ~/domainmate-static.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable domainmate-static.service
  else
    sudo cp ~/domainmate-static.service /etc/systemd/system/
    sudo systemctl daemon-reload
  fi
  
  # Remove old service if it exists
  if [ -f /etc/systemd/system/domainmate.service ]; then
    sudo systemctl stop domainmate.service || true
    sudo systemctl disable domainmate.service || true
    sudo rm /etc/systemd/system/domainmate.service
    sudo systemctl daemon-reload
  fi

  # Start API server first
  sudo systemctl restart domainmate-api.service
  sleep 5
  # Then start static server
  sudo systemctl restart domainmate-static.service

  # Clean up
  rm ~/domainmate-deploy.tar.gz ~/domainmate-api.service ~/domainmate-static.service
EOF

echo "Deployment complete!"
echo ""
echo "Services status:"
echo "- API Server: sudo systemctl status domainmate-api.service"
echo "- Static Server: sudo systemctl status domainmate-static.service"
echo ""
echo "To view logs:"
echo "- API Server: sudo journalctl -u domainmate-api.service -f"
echo "- Static Server: sudo journalctl -u domainmate-static.service -f"
