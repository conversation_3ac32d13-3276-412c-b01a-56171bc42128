import { useState, useEffect } from 'react';
import type { DomainInfo } from '@shared/types';
import { useToast } from '@/hooks/use-toast';

// Type for favorite domain
export interface FavoriteDomain {
  id: number;
  domainName: string;
  tld: string;
  fullDomain: string;
  available: boolean;
  price?: number | string | null;
  createdAt: string;
}

export function useFavorites() {
  const { toast } = useToast();
  const [favorites, setFavorites] = useState<FavoriteDomain[]>([]);

  // Load favorites from localStorage on initial render
  useEffect(() => {
    const storedFavorites = localStorage.getItem('domainFavorites');
    if (storedFavorites) {
      try {
        setFavorites(JSON.parse(storedFavorites));
      } catch (error) {
        // Clear invalid data
        localStorage.removeItem('domainFavorites');
      }
    }
  }, []);

  // Save favorites to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('domainFavorites', JSON.stringify(favorites));
  }, [favorites]);


  const addFavorite = (domain: DomainInfo) => {
    const newFavorite: FavoriteDomain = {
      id: Date.now(),
      domainName: domain.name,
      tld: domain.tld,
      fullDomain: domain.fullDomain,
      available: domain.available,
      price: domain.price,
      createdAt: new Date().toISOString()
    };

    setFavorites(prev => {

      if (prev.some(fav => fav.fullDomain === domain.fullDomain)) {
        return prev;
      }
      return [...prev, newFavorite];
    });

    toast({
      title: 'Domain Favorited',
      description: `${domain.fullDomain} has been added to your favorites.`,
      variant: 'default',
    });
  };


  const removeFavorite = (fullDomain: string) => {
    setFavorites(prev => prev.filter(fav => fav.fullDomain !== fullDomain));

    toast({
      title: 'Domain Removed',
      description: `Domain has been removed from your favorites.`,
      variant: 'default',
    });
  };


  const toggleFavorite = (domain: DomainInfo) => {
    if (isFavorite(domain.fullDomain)) {
      removeFavorite(domain.fullDomain);
    } else {
      addFavorite(domain);
    }
  };


  const isFavorite = (fullDomain: string): boolean => {
    return favorites.some(fav => fav.fullDomain === fullDomain);
  };

  return {
    favorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite,
  };
}
