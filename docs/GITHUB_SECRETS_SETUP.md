# GitHub Secrets Setup Guide

This guide walks you through setting up the required GitHub secrets for automated deployment.

## Step 1: Access GitHub Secrets

1. Go to your GitHub repository
2. Click **Settings** (in the repository, not your profile)
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret** for each secret below

## Step 2: VPS Connection Secrets

### SSH_PRIVATE_KEY
1. On your local machine, generate an SSH key pair (if you don't have one):
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```
2. Copy the **private key** content:
   ```bash
   cat ~/.ssh/id_rsa
   ```
3. Add the **public key** to your VPS:
   ```bash
   ssh-copy-id username@your-vps-ip
   ```
4. In GitHub, create secret `SSH_PRIVATE_KEY` with the private key content

### VPS_IP
- **Name**: `VPS_IP`
- **Value**: Your VPS IP address (e.g., `*************`)

### VPS_USERNAME
- **Name**: `VPS_USERNAME`
- **Value**: Your VPS username (usually `root`)

## Step 3: Database Secrets

### DATABASE_URL
- **Name**: `DATABASE_URL`
- **Value**: Your PostgreSQL connection string
- **Format**: `postgresql://username:password@host:port/database`
- **Example**: `postgresql://myuser:mypass@localhost:5432/domainmate`

## Step 4: AI Service Secrets

### OPENAI_API_KEY
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. **Name**: `OPENAI_API_KEY`
4. **Value**: Your OpenAI API key (starts with `sk-`)

### GEMINI_API_KEY
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. **Name**: `GEMINI_API_KEY`
4. **Value**: Your Gemini API key

### AI_PROVIDER
- **Name**: `AI_PROVIDER`
- **Value**: `openai` or `gemini` (choose your preferred provider)

## Step 5: Auth0 Secrets

### Backend Auth0 Secrets

#### AUTH0_DOMAIN
- **Name**: `AUTH0_DOMAIN`
- **Value**: Your Auth0 domain (e.g., `your-app.auth0.com`)

#### AUTH0_AUDIENCE
- **Name**: `AUTH0_AUDIENCE`
- **Value**: Your Auth0 API identifier (e.g., `https://api.domainmate.net`)

### Frontend Auth0 Secrets

#### NEXT_PUBLIC_AUTH0_DOMAIN
- **Name**: `NEXT_PUBLIC_AUTH0_DOMAIN`
- **Value**: Same as `AUTH0_DOMAIN`

#### NEXT_PUBLIC_AUTH0_CLIENT_ID
- **Name**: `NEXT_PUBLIC_AUTH0_CLIENT_ID`
- **Value**: Your Auth0 application client ID

#### NEXT_PUBLIC_AUTH0_AUDIENCE
- **Name**: `NEXT_PUBLIC_AUTH0_AUDIENCE`
- **Value**: Same as `AUTH0_AUDIENCE`

## Step 6: Verify Secrets

After adding all secrets, you should have these 10 secrets:

✅ **VPS Connection (3)**
- `SSH_PRIVATE_KEY`
- `VPS_IP`
- `VPS_USERNAME`

✅ **Database (1)**
- `DATABASE_URL`

✅ **AI Services (3)**
- `OPENAI_API_KEY`
- `GEMINI_API_KEY`
- `AI_PROVIDER`

✅ **Auth0 (5)**
- `AUTH0_DOMAIN`
- `AUTH0_AUDIENCE`
- `NEXT_PUBLIC_AUTH0_DOMAIN`
- `NEXT_PUBLIC_AUTH0_CLIENT_ID`
- `NEXT_PUBLIC_AUTH0_AUDIENCE`

## Step 7: Test Deployment

1. Push changes to the `main` or `ssg-domainmate` branch
2. Go to **Actions** tab in your GitHub repository
3. Watch the deployment workflow run
4. Check the logs for any errors

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify `SSH_PRIVATE_KEY` is the complete private key
   - Ensure public key is added to VPS `~/.ssh/authorized_keys`
   - Check `VPS_IP` and `VPS_USERNAME` are correct

2. **Database Connection Failed**
   - Verify `DATABASE_URL` format is correct
   - Ensure database server is accessible from VPS
   - Check database credentials

3. **AI API Errors**
   - Verify API keys are valid and have sufficient credits
   - Check `AI_PROVIDER` is set to `openai` or `gemini`

4. **Auth0 Errors**
   - Ensure all Auth0 secrets match your Auth0 application settings
   - Verify domain and audience are correct

### Getting Help

If you encounter issues:
1. Check the GitHub Actions logs for detailed error messages
2. SSH into your VPS and check service logs:
   ```bash
   sudo journalctl -u domainmate-api.service -f
   sudo journalctl -u domainmate-static.service -f
   ```
3. Verify all secrets are set correctly in GitHub

## Security Notes

- Never commit secrets to your repository
- Regularly rotate API keys and SSH keys
- Use environment-specific secrets for staging/production
- Monitor API usage to detect unauthorized access 