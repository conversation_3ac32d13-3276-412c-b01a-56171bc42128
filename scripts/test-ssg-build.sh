#!/bin/bash

# Test SSG Build Script
# This script tests the SSG build process locally

set -e

echo "🚀 Testing SSG Build Process..."

# Check if required environment variables are set
echo "📋 Checking environment variables..."
if [ -z "$NEXT_PUBLIC_AUTH0_DOMAIN" ]; then
    echo "⚠️  Warning: NEXT_PUBLIC_AUTH0_DOMAIN not set"
fi

if [ -z "$NEXT_PUBLIC_AUTH0_CLIENT_ID" ]; then
    echo "⚠️  Warning: NEXT_PUBLIC_AUTH0_CLIENT_ID not set"
fi

if [ -z "$NEXT_PUBLIC_AUTH0_AUDIENCE" ]; then
    echo "⚠️  Warning: NEXT_PUBLIC_AUTH0_AUDIENCE not set"
fi

# Set default values for testing
export NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL:-"http://localhost:3000"}
export NEXT_PUBLIC_AUTH0_DOMAIN=${NEXT_PUBLIC_AUTH0_DOMAIN:-"test.auth0.com"}
export NEXT_PUBLIC_AUTH0_CLIENT_ID=${NEXT_PUBLIC_AUTH0_CLIENT_ID:-"test_client_id"}
export NEXT_PUBLIC_AUTH0_AUDIENCE=${NEXT_PUBLIC_AUTH0_AUDIENCE:-"test_audience"}

echo "✅ Environment variables set"

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf out/
rm -rf .next/

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building SSG application..."
npm run build

# Check if build was successful
if [ ! -d "out" ]; then
    echo "❌ Build failed: out directory not found"
    exit 1
fi

if [ ! -f "out/index.html" ]; then
    echo "❌ Build failed: index.html not found"
    exit 1
fi

echo "✅ SSG build successful"

# Create runtime config (simulating deployment)
echo "⚙️  Creating runtime config..."
cat > out/config.js << EOF
window.ENV = {
  SITE_URL: "${NEXT_PUBLIC_SITE_URL}",
  AUTH0_DOMAIN: "${NEXT_PUBLIC_AUTH0_DOMAIN}",
  AUTH0_CLIENT_ID: "${NEXT_PUBLIC_AUTH0_CLIENT_ID}",
  AUTH0_AUDIENCE: "${NEXT_PUBLIC_AUTH0_AUDIENCE}"
};
EOF

# Update index.html to include config
echo "📝 Updating index.html..."
sed -i.bak '/<\/head>/i \    <script src="/config.js"></script>' out/index.html

# Create static server for testing
echo "🖥️  Creating test static server..."
mkdir -p test-server
cat > test-server/server.js << 'EOF'
const express = require('express');
const path = require('path');
const app = express();
const port = 8080;

// Serve static files from out directory
app.use(express.static(path.join(__dirname, '..', 'out')));

// Handle client-side routing - serve index.html for all routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '..', 'out', 'index.html'));
});

app.listen(port, () => {
  console.log(`Test server running at http://localhost:${port}`);
  console.log('Press Ctrl+C to stop');
});
EOF

cat > test-server/package.json << 'EOF'
{
  "name": "test-static-server",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.21.2"
  }
}
EOF

# Install test server dependencies
echo "📦 Installing test server dependencies..."
cd test-server && npm install && cd ..

echo "✅ Test setup complete!"
echo ""
echo "🎉 SSG build test successful!"
echo ""
echo "To test the static site:"
echo "  1. Run: cd test-server && node server.js"
echo "  2. Open: http://localhost:8080"
echo "  3. Or use Python: cd out && python -m http.server 8080"
echo ""
echo "Files generated:"
echo "  - out/ (static site files)"
echo "  - out/config.js (runtime configuration)"
echo "  - test-server/ (test static server)"
