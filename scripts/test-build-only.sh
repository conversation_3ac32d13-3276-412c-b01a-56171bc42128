#!/bin/bash

# Quick Build Test Script for Linux/Ubuntu
# Tests only the build process without running servers

set -e

echo "🔨 Testing DomainMate Build Process"
echo "==================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Run this script from the project root."
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf out dist

# Test static site build
echo "🔨 Testing static site build..."
if npm run build; then
    echo "✅ Static site build successful"
    file_count=$(find out -type f | wc -l)
    echo "   Generated files in: out/"
    echo "   Files count: $file_count"
else
    echo "❌ Static site build failed"
    exit 1
fi

# Test API server build
echo "🔨 Testing API server build..."
if npm run build:server; then
    echo "✅ API server build successful"
    file_size=$(du -h dist/index.js | cut -f1)
    echo "   Generated files in: dist/"
    echo "   Main file: dist/index.js ($file_size)"
else
    echo "❌ API server build failed"
    exit 1
fi

# Verify critical files exist
echo "🔍 Verifying build outputs..."

# Check static files
if [ -f "out/index.html" ]; then
    echo "✅ Static site index.html exists"
else
    echo "❌ Static site index.html missing"
    exit 1
fi

if [ -d "out/_next" ]; then
    echo "✅ Next.js assets directory exists"
else
    echo "❌ Next.js assets directory missing"
    exit 1
fi

# Check API server
if [ -f "dist/index.js" ]; then
    echo "✅ API server bundle exists"
else
    echo "❌ API server bundle missing"
    exit 1
fi

# Test if the API server bundle is valid JavaScript
if node -c "dist/index.js"; then
    echo "✅ API server bundle is valid JavaScript"
else
    echo "❌ API server bundle has syntax errors"
    exit 1
fi

echo ""
echo "🎉 All builds successful!"
echo ""

# Calculate directory sizes
out_size=$(du -sh out | cut -f1)
dist_size=$(du -sh dist | cut -f1)

echo "📊 Build Summary:"
echo "   Static files: out/ ($out_size)"
echo "   API server:   dist/ ($dist_size)"
echo ""
echo "🚀 Ready for deployment!"
echo ""
echo "Next steps:"
echo "   1. Test locally: ./scripts/test-deployment-locally.sh"
echo "   2. Deploy manually: ./scripts/deploy.sh <username> <ip>"
echo "   3. Deploy via GitHub Actions: git push origin main" 