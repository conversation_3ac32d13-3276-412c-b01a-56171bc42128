import Head from 'next/head';
import { GetStaticProps, GetStaticPaths } from 'next';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { blogPosts } from "@/lib/blog-posts";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  date: string;
  readTime: string;
}

interface BlogPostPageProps {
  post: BlogPost;
}

export default function BlogPostPage({ post }: BlogPostPageProps) {
  return (
    <>
      <Head>
        <title>{post.title} | DomainMate Blog</title>
        <meta name="description" content={post.excerpt} />
        <meta name="keywords" content="domain name, blog, business tips, domain advice" />

        {/* Open Graph tags */}
        <meta property="og:title" content={`${post.title} | DomainMate Blog`} />
        <meta property="og:description" content={post.excerpt} />
        <meta property="og:url" content={`https://domainmate.net/blog/${post.id}`} />
        <meta property="og:type" content="article" />
        <meta property="article:published_time" content={post.date} />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${post.title} | DomainMate Blog`} />
        <meta name="twitter:description" content={post.excerpt} />

        {/* Canonical URL */}
        <link rel="canonical" href={`https://domainmate.net/blog/${post.id}`} />

        {/* Structured Data for Article */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              "headline": post.title,
              "description": post.excerpt,
              "url": `https://domainmate.net/blog/${post.id}`,
              "datePublished": post.date,
              "author": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              },
              "publisher": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              },
              "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": `https://domainmate.net/blog/${post.id}`
              }
            })
          }}
        />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="mb-8">
              <Link href="/blog">
                <Button variant="ghost" className="mb-6">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Blog
                </Button>
              </Link>

              <article>
                <header className="mb-8">
                  <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                    {post.title}
                  </h1>

                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-6">
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                    <span>•</span>
                    <span>{post.readTime}</span>
                  </div>
                </header>

                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
              </article>
            </div>

            <div className="border-t border-gray-200 pt-8">
              <Link href="/blog">
                <Button variant="outline">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to All Posts
                </Button>
              </Link>
            </div>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

// This function gets called at build time to determine which paths to pre-render
export const getStaticPaths: GetStaticPaths = async () => {
  const paths = blogPosts.map((post) => ({
    params: { id: post.id },
  }));

  return {
    paths,
    fallback: false, // Any paths not returned by getStaticPaths will result in a 404 page
  };
};

// This function gets called at build time for each path
export const getStaticProps: GetStaticProps<BlogPostPageProps> = async ({ params }) => {
  const post = blogPosts.find((post) => post.id === params?.id);

  if (!post) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      post,
    },
  };
};
