import Head from 'next/head';
import { GetStaticProps } from 'next';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { Card, CardContent } from "@/components/ui/card";

interface PrivacyPolicyPageProps {
  lastUpdated: string;
}

export default function PrivacyPolicyPage({ lastUpdated }: PrivacyPolicyPageProps) {
  return (
    <>
      <Head>
        <title>Privacy Policy | DomainMate</title>
        <meta
          name="description"
          content="Read DomainMate's privacy policy to understand how we collect, use, and protect your personal information."
        />
        <meta name="robots" content="index, follow" />

        {/* Open Graph tags */}
        <meta property="og:title" content="Privacy Policy | DomainMate" />
        <meta property="og:description" content="Read DomainMate's privacy policy to understand how we collect, use, and protect your personal information." />
        <meta property="og:url" content="https://domainmate.net/privacy-policy" />
        <meta property="og:type" content="website" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/privacy-policy" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebPage",
              "name": "Privacy Policy",
              "description": "DomainMate's privacy policy explaining how we collect, use, and protect your personal information",
              "url": "https://domainmate.net/privacy-policy",
              "publisher": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              },
              "dateModified": lastUpdated
            })
          }}
        />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="mb-8">
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Privacy Policy
              </h1>
              <p className="text-lg text-gray-600">
                Last updated: {new Date(lastUpdated).toLocaleDateString()}
              </p>
            </div>

            <Card>
              <CardContent className="p-8">
                <div className="prose prose-lg max-w-none">
                  <h2>Introduction</h2>
                  <p>
                    At DomainMate, we take your privacy seriously. This Privacy Policy explains how we collect,
                    use, disclose, and safeguard your information when you visit our website and use our services.
                  </p>

                  <h2>Information We Collect</h2>
                  <h3>Personal Information</h3>
                  <p>
                    We may collect personal information that you voluntarily provide to us when you:
                  </p>
                  <ul>
                    <li>Create an account</li>
                    <li>Use our domain search services</li>
                    <li>Save domains to your favorites</li>
                    <li>Contact us for support</li>
                  </ul>

                  <h3>Automatically Collected Information</h3>
                  <p>
                    When you visit our website, we may automatically collect certain information about your device,
                    including information about your web browser, IP address, time zone, and some of the cookies
                    that are installed on your device.
                  </p>

                  <h2>How We Use Your Information</h2>
                  <p>
                    We use the information we collect to:
                  </p>
                  <ul>
                    <li>Provide, operate, and maintain our services</li>
                    <li>Improve and personalize your experience</li>
                    <li>Process your requests and transactions</li>
                    <li>Send you technical notices and support messages</li>
                    <li>Respond to your comments and questions</li>
                  </ul>

                  <h2>Information Sharing</h2>
                  <p>
                    We do not sell, trade, or otherwise transfer your personal information to third parties without
                    your consent, except as described in this Privacy Policy.
                  </p>

                  <h2>Data Security</h2>
                  <p>
                    We implement appropriate technical and organizational security measures to protect your personal
                    information against unauthorized access, alteration, disclosure, or destruction.
                  </p>

                  <h2>Cookies and Tracking Technologies</h2>
                  <p>
                    We use cookies and similar tracking technologies to track activity on our website and hold
                    certain information to improve your experience.
                  </p>

                  <h2>Third-Party Services</h2>
                  <p>
                    Our website may contain links to third-party websites or services. We are not responsible
                    for the privacy practices of these third parties.
                  </p>

                  <h2>Your Rights</h2>
                  <p>
                    Depending on your location, you may have certain rights regarding your personal information,
                    including the right to access, update, or delete your information.
                  </p>

                  <h2>Changes to This Privacy Policy</h2>
                  <p>
                    We may update this Privacy Policy from time to time. We will notify you of any changes by
                    posting the new Privacy Policy on this page and updating the "Last updated" date.
                  </p>

                  <h2>Contact Us</h2>
                  <p>
                    If you have any questions about this Privacy Policy, please contact us through our website
                    or by email.
                  </p>
                </div>
              </CardContent>
            </Card>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

// This function gets called at build time for static generation
export const getStaticProps: GetStaticProps<PrivacyPolicyPageProps> = async () => {
  return {
    props: {
      lastUpdated: new Date().toISOString(),
    },
  };
};
