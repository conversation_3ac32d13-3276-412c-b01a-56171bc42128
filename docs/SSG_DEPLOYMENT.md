# SSG Deployment Guide

## Overview

DomainMate has been successfully migrated from Client-Side Rendered (CSR) React with Express.js backend to Static Site Generated (SSG) architecture using Next.js. This document explains the deployment pipeline changes.

## Architecture Changes

### Before (CSR + Express.js)
- React app served by Express.js server
- Server-side API routes
- Dynamic server rendering
- Required Node.js runtime on server

### After (SSG + Static Server)
- Static HTML/CSS/JS files generated at build time
- Client-side API calls to external services
- Pre-rendered pages for better SEO
- Simple static file server

## Deployment Pipeline Changes

### Build Process
1. **Environment Variables**: Set at build time for Next.js
2. **Static Generation**: `next build` creates static files in `out/` directory
3. **Runtime Config**: Dynamic configuration injected via `config.js`
4. **Static Server**: Simple Express.js server to serve static files

### Deployment Package
- `out/` - Static site files
- `server-static/` - Minimal static file server
- `package.json` - Dependencies for static server only

### Server Configuration
- **Systemd Service**: Runs simple static file server
- **Working Directory**: `/root/domainmate/server-static`
- **Entry Point**: `server.js` (Express.js static server)
- **Port**: 3000

## Key Benefits

1. **Better SEO**: Pre-rendered HTML pages
2. **Faster Loading**: Static files served directly
3. **Reduced Server Load**: No server-side rendering
4. **Easier Scaling**: Can use CDN for static assets
5. **Lower Resource Usage**: Minimal server requirements

## Local Development

```bash
# Development with hot reload
npm run dev

# Build static site
npm run build

# Test static build locally
cd out && python -m http.server 8000
```

## Production Deployment

The GitHub Actions workflow automatically:
1. Builds the static site with environment variables
2. Creates runtime configuration
3. Packages static files and minimal server
4. Deploys to VPS with systemd service

## Environment Variables

### Build Time (Next.js)
- `NEXT_PUBLIC_SITE_URL`
- `NEXT_PUBLIC_AUTH0_DOMAIN`
- `NEXT_PUBLIC_AUTH0_CLIENT_ID`
- `NEXT_PUBLIC_AUTH0_AUDIENCE`

### Runtime (Injected via config.js)
- `SITE_URL`
- `AUTH0_DOMAIN`
- `AUTH0_CLIENT_ID`
- `AUTH0_AUDIENCE`

## Troubleshooting

### Build Issues
- Check Next.js configuration in `next.config.js`
- Verify environment variables are set
- Ensure all imports are client-side compatible

### Deployment Issues
- Check systemd service status: `sudo systemctl status domainmate.service`
- View logs: `sudo journalctl -u domainmate.service -f`
- Verify static files exist in `/root/domainmate/out/`

### Static Server Issues
- Check if Express.js dependencies are installed
- Verify server.js file exists and is executable
- Check port 3000 is available
