# Database connection
DATABASE_URL=postgres://username:password@localhost:5432/domainmate

# OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API key
GEMINI_API_KEY=your_gemini_api_key_here

# AI provider selection (openai or gemini)
AI_PROVIDER=openai

# Auth0 Configuration
# Server-side variables (not exposed to the browser)
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier

# Client-side variables (exposed to the browser, must be prefixed with NEXT_PUBLIC_)
NEXT_PUBLIC_AUTH0_DOMAIN=your-auth0-domain.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-auth0-client-id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
NEXT_PUBLIC_SITE_URL=your_site_url_here
