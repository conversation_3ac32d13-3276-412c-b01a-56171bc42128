import type { DomainInfo, GroupedDomainInfo } from "@shared/types";

// Generate a random color from the domain name for branding purposes
export const getDomainColor = (domain: string): string => {
  let hash = 0;

  for (let i = 0; i < domain.length; i++) {
    hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash;
  }

  const hue = hash % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

// Format price for display
export const formatPrice = (price?: number | string | null): string => {
  if (price === undefined || price === null) return 'N/A';

  // Convert to number if it's a string
  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

  // Check if it's a valid number
  if (isNaN(numericPrice)) return 'N/A';

  if (numericPrice >= 1000) {
    return `$${(numericPrice / 1000).toFixed(2)}k`;
  }

  return `$${numericPrice.toFixed(2)}`;
};

// Get suggestion types based on domain name
export const getSuggestionTypes = (searchTerm: string): { title: string, description: string, examples: string[] }[] => {
  if (!searchTerm) return [];

  const term = searchTerm.toLowerCase().replace(/[^a-z0-9]/g, '');

  return [
    {
      title: "Add Prefixes",
      description: "Try adding words like 'get', 'my', 'best', 'the'",
      examples: [`get${term}.com`, `my${term}.com`, `the${term}.com`]
    },
    {
      title: "Try Synonyms",
      description: "Use related words to find more options",
      examples: [`${term}hub.com`, `${term}spot.com`, `${term}zone.com`]
    },
    {
      title: "Alternative TLDs",
      description: "Explore different domain extensions",
      examples: [`.io`, `.app`, `.dev`]
    }
  ];
};

// Get category words
export const getCategories = (): { name: string, displayName: string }[] => {
  return [
    { name: "tech", displayName: "Technology" },
    { name: "business", displayName: "Business" },
    { name: "creative", displayName: "Creative" },
    { name: "health", displayName: "Health" },
    { name: "ecommerce", displayName: "E-commerce" }
  ];
};

// Get sample search for category
export const getCategorySample = (category: string): string => {
  const samples: Record<string, string> = {
    tech: "productivity app for remote teams",
    business: "business consulting platform",
    creative: "digital art portfolio platform",
    health: "personalized fitness coaching app",
    ecommerce: "handmade jewelry marketplace"
  };

  return samples[category] || "domain name";
};

// Group domains by base name
export const groupDomainsByName = (domains: DomainInfo[]): GroupedDomainInfo[] => {
  const groupedMap = new Map<string, DomainInfo[]>();

  // Group domains by their base name
  domains.forEach(domain => {
    if (!groupedMap.has(domain.name)) {
      groupedMap.set(domain.name, []);
    }
    groupedMap.get(domain.name)?.push(domain);
  });

  // Convert the map to an array of GroupedDomainInfo objects
  return Array.from(groupedMap.entries()).map(([baseName, domainsList]) => {
    const availableTlds = domainsList
      .filter(d => d.available)
      .map(d => d.tld);

    const unavailableTlds = domainsList
      .filter(d => !d.available)
      .map(d => d.tld);

    // Find the lowest price among available domains
    const availableDomains = domainsList.filter(d => d.available);
    const lowestPrice = availableDomains.length > 0
      ? Math.min(...availableDomains.filter(d => d.price !== undefined).map(d => {
          const price = d.price;
          if (typeof price === 'string') {
            const parsed = parseFloat(price);
            return isNaN(parsed) ? Infinity : parsed;
          }
          return typeof price === 'number' ? price : Infinity;
        }))
      : undefined;

    // Use the type from the first domain as the group type
    const type = domainsList[0]?.type;

    return {
      baseName,
      domains: domainsList,
      availableTlds,
      unavailableTlds,
      lowestPrice: lowestPrice === Infinity ? undefined : lowestPrice,
      type
    };
  });
};

// FAQ questions
export const getFaqQuestions = (): { question: string, answer: string }[] => {
  return [
    {
      question: "How does DomainMate's AI domain name generator work?",
      answer: "Our AI domain name generator uses advanced large language models to analyze your business concept and generate highly creative, memorable domain suggestions. Unlike basic keyword tools that many competitors claim as 'AI', we use actual AI technology to understand context and produce truly innovative domain name ideas that perfectly capture your brand identity."
    },
    {
      question: "How accurate is the domain availability check in this domain name generator?",
      answer: "Our domain name generator checks every domain suggestion in real-time to provide the most accurate availability information possible. We ensure you only see domains you can actually register right now at standard prices. We don't waste your time showing unavailable domains or premium domains with inflated prices."
    },
    {
      question: "What makes a good domain name for my business or project?",
      answer: "A great domain name is distinctive, memorable, and captures the essence of your brand. It should be easy to spell when heard, relatively short, and evoke the right emotions for your business. The most successful domain names are often creative and unique rather than generic. Our AI domain name generator is specifically designed to create names that balance creativity with practical considerations like spelling and memorability."
    },
    {
      question: "Why is DomainMate's domain name generator better than other domain generators?",
      answer: "Our domain name generator prioritizes creativity and uniqueness, using the latest AI models to create truly distinctive domain names. Unlike competitors who use simple keyword combinations or templates, our AI understands the context and essence of your business to generate names with personality. We only show domains you can actually register at standard prices, and we verify availability in real-time."
    },
    {
      question: "Can I use this domain name generator for free?",
      answer: "Yes! Our AI domain name generator is completely free to use. We make money through affiliate commissions when you register a domain through our partner links, which means you get a powerful domain name generator tool at no cost to you."
    },
    {
      question: "How do I choose the best domain name from the generator results?",
      answer: "When selecting from our domain name generator results, look for names that are memorable, easy to spell, and relevant to your business. Consider shorter names (under 15 characters) and avoid hyphens or numbers unless they're part of your brand. Test potential names by saying them out loud and asking others if they can spell them correctly after hearing them. Our domain name generator provides multiple options so you can find the perfect match."
    }
  ];
};
