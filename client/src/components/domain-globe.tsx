import { useEffect, useRef } from 'react';

interface DomainGlobeProps {
  className?: string;
}

export function DomainGlobe({ className = '' }: DomainGlobeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 300;
    canvas.height = 300;

    // Globe parameters
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 100;

    // Domains to display around the globe
    const domains = [
      '.com', '.ai', '.io', '.app',
      '.org', '.net', '.tech',
      '.dev', '.design', '.me'
    ];

    // Animation state
    let rotation = 0;
    let mouseX = centerX;
    let mouseY = centerY;

    // Track mouse position for interactive rotation
    canvas.addEventListener('mousemove', (e) => {
      const rect = canvas.getBoundingClientRect();
      mouseX = e.clientX - rect.left;
      mouseY = e.clientY - rect.top;
    });

    // Draw function
    function draw() {
      if (!ctx || !canvas) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Calculate tilt based on mouse position
      const tiltX = (mouseY - centerY) / 100;
      const tiltY = (mouseX - centerX) / 100;

      // Draw globe with gradient
      const gradient = ctx.createRadialGradient(
        centerX - 20, centerY - 20, 5,
        centerX, centerY, radius
      );
      gradient.addColorStop(0, 'rgba(13, 138, 247, 0.8)');  // Primary color
      gradient.addColorStop(0.5, 'rgba(105, 106, 235, 0.5)');
      gradient.addColorStop(1, 'rgba(138, 85, 247, 0.2)');  // Secondary color

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 15;
      ctx.shadowOffsetX = 5;
      ctx.shadowOffsetY = 5;
      ctx.fill();
      ctx.shadowColor = 'transparent';

      // Draw latitude lines
      for (let i = 1; i <= 5; i++) {
        const latRadius = (radius / 5) * i;
        ctx.beginPath();
        ctx.ellipse(
          centerX,
          centerY,
          latRadius,
          latRadius * Math.cos(tiltX),
          0, 0, Math.PI * 2
        );
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.stroke();
      }

      // Draw longitude lines
      for (let i = 0; i < 8; i++) {
        const angle = (i * Math.PI / 4) + rotation;
        ctx.beginPath();
        ctx.ellipse(
          centerX,
          centerY,
          radius,
          radius * Math.cos(tiltY),
          angle, 0, Math.PI
        );
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.stroke();
      }

      // Draw domain names orbiting the globe
      domains.forEach((domain, i) => {
        const angle = (i * (Math.PI * 2) / domains.length) + rotation;
        const orbitRadius = radius + 30;

        // Apply perspective effect based on angle
        const x = centerX + Math.cos(angle) * orbitRadius;
        const y = centerY + Math.sin(angle) * orbitRadius * Math.cos(tiltX);

        // Scale and opacity based on y-position (perspective)
        const scale = 0.7 + (Math.sin(angle) * 0.3);
        const opacity = 0.4 + (Math.sin(angle) * 0.6);

        ctx.save();
        ctx.translate(x, y);
        ctx.scale(scale, scale);

        // Draw circle behind text
        ctx.beginPath();
        const textWidth = ctx.measureText(domain).width;
        ctx.roundRect(-textWidth/2 - 10, -10, textWidth + 20, 20, 10);
        ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
        ctx.fill();

        // Draw domain text
        ctx.fillStyle = `rgba(40, 40, 120, ${opacity})`;
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(domain, 0, 0);

        ctx.restore();
      });

      // Update rotation for animation
      rotation += 0.005;

      requestAnimationFrame(draw);
    }

    // Start animation
    draw();

  }, []);

  return (
    <canvas
      ref={canvasRef}
      width="300"
      height="300"
      className={`${className} animate-float-slow`}
    />
  );
}