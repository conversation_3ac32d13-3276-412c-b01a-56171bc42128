import 'dotenv/config';
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Request logging middleware for development
if (process.env.NODE_ENV === "development") {
  app.use((req, res, next) => {
    const start = Date.now();
    const path = req.path;

    res.on("finish", () => {
      const duration = Date.now() - start;
      if (path.startsWith("/api")) {
        log(`${req.method} ${path} ${res.statusCode} in ${duration}ms`);
      }
    });

    next();
  });
}

(async () => {
  const server = await registerRoutes(app);

  // Global error handling middleware
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    // Only send response if headers haven't been sent yet
    if (!res.headersSent) {
      res.status(status).json({ message });
    }

    // Log the error in development mode
    if (process.env.NODE_ENV === "development") {
      console.error("Error:", err);
    }
  });

  // Setup Vite or static file serving only in development
  // In production, static files are served by a separate static server
  if (process.env.NODE_ENV === "development") {
    await setupVite(app, server);
  }

  // Start the server
  // In production, use PORT environment variable (set to 3001 by systemd)
  // In development, use 3000 for compatibility with existing setup
  const port = process.env.PORT || (process.env.NODE_ENV === "production" ? 3001 : 3000);
  server.listen(port, () => {
    log(`API Server running on port ${port}`);
  });
})();
