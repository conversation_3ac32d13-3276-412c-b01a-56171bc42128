/**
 * Get environment variables from both build-time and runtime sources
 */
function getEnvironmentVariables() {
  const runtimeEnv = typeof window !== 'undefined' ? (window as any).ENV || {} : {};

  // Determine the site URL based on environment
  const getSiteUrl = () => {
    // First check if NEXT_PUBLIC_SITE_URL is explicitly set
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL ||
                   (typeof window !== 'undefined' && (window as any).ENV?.SITE_URL);

    if (siteUrl) {
      return siteUrl;
    }

    // Check if we're in development mode
    const isDev = process.env.NODE_ENV === 'development' ||
                  (typeof window !== 'undefined' && window.location.hostname === 'localhost');

    // Otherwise, use window.location.origin in development and domainmate.net in production
    return isDev && typeof window !== 'undefined'
      ? window.location.origin
      : 'https://domainmate.net';
  };

  const buildTimeEnv = {
    SITE_URL: typeof window !== 'undefined' ? getSiteUrl() : ''
  };

  // Log the site URL to help with debugging
  if (typeof window !== 'undefined') {
    const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
    console.log(`Environment: ${isDev ? 'Development' : 'Production'}`);
    console.log('Environment SITE_URL:', buildTimeEnv.SITE_URL);
  }

  const combinedEnv = {
    ...buildTimeEnv,
    ...runtimeEnv
  };

  // No required environment variables to check

  return combinedEnv;
}


export const env = getEnvironmentVariables();
