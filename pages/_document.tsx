import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Defer Google Analytics to not block rendering */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Delayed loading of Google Analytics
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var gaScript = document.createElement('script');
                  gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-H4NYY7F33M';
                  gaScript.async = true;
                  document.head.appendChild(gaScript);

                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-H4NYY7F33M');
                }, 1000); // Delay by 1 second after page load
              });
            `,
          }}
        />
        
        {/* Defer Google AdSense loading */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var adScript = document.createElement('script');
                  adScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5598928771673352';
                  adScript.async = true;
                  adScript.crossOrigin = 'anonymous';
                  document.head.appendChild(adScript);
                }, 2000); // Delay by 2 seconds after page load
              });
            `,
          }}
        />
      </Head>
      <body>
        <Main />
        <NextScript />
        
        {/* Register service worker for caching and offline support */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Register service worker for production only
              if ('serviceWorker' in navigator && window.location.hostname !== 'localhost') {
                window.addEventListener('load', () => {
                  navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                      console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(error => {
                      console.log('ServiceWorker registration failed: ', error);
                    });
                });
              }
            `,
          }}
        />
        
        {/* Add support for native lazy-loading */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img:not([loading])');
                images.forEach(img => {
                  if (img.classList.contains('critical')) return;
                  img.setAttribute('loading', 'lazy');
                });
              });
            `,
          }}
        />
        
        {/* Mark the main description as high priority for the browser */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('scheduler' in window && 'postTask' in window.scheduler) {
                scheduler.postTask(() => {}, { priority: 'user-visible' });
              }
            `,
          }}
        />
      </body>
    </Html>
  );
}
