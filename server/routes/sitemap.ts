import { Router } from 'express';
import { blogPosts } from '../../client/src/lib/blog-posts';

const router = Router();

/**
 * Generate a dynamic sitemap.xml
 * This ensures that only canonical URLs are included in the sitemap
 */
router.get('/sitemap.xml', (req, res) => {
  // Set content type to XML
  res.header('Content-Type', 'application/xml');

  // Get the base URL
  const baseUrl = process.env.NODE_ENV === 'production' 
    ? 'https://domainmate.net' 
    : `${req.protocol}://${req.get('host')}`;

  // Start building the sitemap
  let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
  sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Add home page
  sitemap += `  <url>\n`;
  sitemap += `    <loc>${baseUrl}/</loc>\n`;
  sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
  sitemap += `    <changefreq>weekly</changefreq>\n`;
  sitemap += `    <priority>1.0</priority>\n`;
  sitemap += `  </url>\n`;

  // Add blog index page
  sitemap += `  <url>\n`;
  sitemap += `    <loc>${baseUrl}/blog</loc>\n`;
  sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
  sitemap += `    <changefreq>weekly</changefreq>\n`;
  sitemap += `    <priority>0.8</priority>\n`;
  sitemap += `  </url>\n`;

  // Add blog posts
  blogPosts.forEach(post => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${baseUrl}/blog/${post.id}</loc>\n`;
    sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
    sitemap += `    <changefreq>monthly</changefreq>\n`;
    sitemap += `    <priority>0.7</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Add profile page
  sitemap += `  <url>\n`;
  sitemap += `    <loc>${baseUrl}/profile</loc>\n`;
  sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
  sitemap += `    <changefreq>monthly</changefreq>\n`;
  sitemap += `    <priority>0.6</priority>\n`;
  sitemap += `  </url>\n`;

  // Add privacy policy page
  sitemap += `  <url>\n`;
  sitemap += `    <loc>${baseUrl}/privacy-policy</loc>\n`;
  sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
  sitemap += `    <changefreq>monthly</changefreq>\n`;
  sitemap += `    <priority>0.5</priority>\n`;
  sitemap += `  </url>\n`;

  // Add terms of service page
  sitemap += `  <url>\n`;
  sitemap += `    <loc>${baseUrl}/terms-of-service</loc>\n`;
  sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
  sitemap += `    <changefreq>monthly</changefreq>\n`;
  sitemap += `    <priority>0.5</priority>\n`;
  sitemap += `  </url>\n`;

  // Close the sitemap
  sitemap += '</urlset>';

  // Send the sitemap
  res.send(sitemap);
});

export default router;
