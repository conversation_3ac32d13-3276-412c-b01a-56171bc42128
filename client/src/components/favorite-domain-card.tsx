import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState, useRef } from "react";
import { Check, ShoppingCart, Heart, ChevronUp } from "lucide-react";
import { useFavorites, FavoriteDomain } from "@/components/favorites-provider";
import { formatPrice } from "@/lib/domain-utils";
import { CelebrationEffect } from "@/components/ui/celebration-effect";

interface FavoriteDomainCardProps {
  domain: FavoriteDomain;
  index?: number;
}

export function FavoriteDomainCard({ domain, index = 0 }: FavoriteDomainCardProps) {
  const [showCelebration, setShowCelebration] = useState(false);
  const [expandedTlds, setExpandedTlds] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { removeFavorite } = useFavorites();

  const hasRelatedTlds = domain.relatedTlds && domain.relatedTlds.length > 0;
  const allTlds = [
    {
      tld: domain.tld,
      fullDomain: domain.fullDomain,
      available: domain.available,
      price: domain.price
    },
    ...(domain.relatedTlds || [])
  ].filter(tld => tld.available);

  const statusColor = domain.available
    ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white"
    : "bg-gradient-to-r from-red-500 to-rose-600 text-white";

  const statusText = domain.available ? "Available" : "Unavailable";

  const opacity = !domain.available ? "opacity-75" : "";

  const infoLabel = domain.available ? "Registration" : "Registered on";
  const infoValue = domain.available ? `${formatPrice(domain.price !== undefined ? domain.price : null)}/year` : "Unknown";

  const triggerCelebration = () => {
    if (domain.available) {
      setShowCelebration(true);
    }
  };

  return (
    <div
      className="relative transform transition-all duration-500 animate-scale-in"
      style={{ animationDelay: `${index * 0.1}s` }}
      ref={cardRef}
    >
      {/* Celebration effect */}
      <CelebrationEffect
        show={showCelebration && domain.available}
        onComplete={() => setShowCelebration(false)}
      />

      <div className="relative">
        <div
          className={`${opacity} transition-all duration-300 relative overflow-hidden rounded-xl shadow-lg`}
        >
          <Card className="border-0 bg-transparent overflow-hidden h-full">
            <div className="flex justify-between items-center border-b p-3 sm:p-4 relative z-10 transition-all duration-300">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full text-red-500 hover:text-red-600"
                  onClick={() => removeFavorite(domain.fullDomain)}
                  title="Remove from favorites"
                >
                  <Heart className="h-4 w-4" fill="currentColor" />
                </Button>
                <h3 className="font-medium text-base sm:text-lg transition-all duration-300 break-words">
                  {domain.fullDomain}
                  {hasRelatedTlds && domain.relatedTlds && (
                    <span className="text-gray-500 text-sm ml-1">
                      (+{domain.relatedTlds.length} more)
                    </span>
                  )}
                </h3>
              </div>
              <Badge className={`${statusColor} shadow transition-all duration-300`}>
                {domain.available && <Check className="w-3 h-3 mr-1" />}
                {statusText}
              </Badge>
            </div>

            <CardContent className="p-3 sm:p-4 relative z-10 transition-all duration-300">
              <div className="flex justify-between items-center mb-3 sm:mb-4">
                <span className="text-xs sm:text-sm text-gray-600 transition-all duration-300">{infoLabel}</span>
                <span className="text-xs sm:text-sm font-medium transition-all duration-300">{infoValue}</span>
              </div>

              {/* TLD options */}
              {allTlds.length > 0 && (
                <div className="mb-3 sm:mb-4">
                  <div className="flex flex-wrap gap-1.5 sm:gap-2 mb-2">
                    {allTlds.slice(0, expandedTlds ? undefined : 3).map(tld => {
                      const price = tld.price ? formatPrice(tld.price) : 'N/A';

                      return (
                        <Button
                          key={tld.tld}
                          variant="outline"
                          size="sm"
                          className="bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center gap-1 text-xs sm:text-sm py-1 h-auto"
                          onClick={() => {
                            triggerCelebration();
                            window.open(`https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D${encodeURIComponent(tld.fullDomain)}`);
                          }}
                        >
                          <ShoppingCart className="w-3 h-3" />
                          <span>.{tld.tld}</span>
                          <span className="text-xs opacity-75 ml-1">{price}/yr</span>
                        </Button>
                      );
                    })}

                    {!expandedTlds && allTlds.length > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                        onClick={() => setExpandedTlds(true)}
                      >
                        +{allTlds.length - 3} more
                      </Button>
                    )}

                    {expandedTlds && allTlds.length > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                        onClick={() => setExpandedTlds(false)}
                      >
                        <ChevronUp className="w-3 h-3 mr-1" />
                        Show less
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* Register button for main domain */}
              <div>
                {domain.available ? (
                  <Button
                    className="w-full bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center justify-center gap-1 transition-all duration-300"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      triggerCelebration();
                      window.open(`https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D${encodeURIComponent(domain.fullDomain)}`);
                    }}
                  >
                    <ShoppingCart className="w-3 h-3" />
                    <span>Register Now</span>
                    <span className="text-xs opacity-75 ml-1">{formatPrice(domain.price)}/yr</span>
                  </Button>
                ) : (
                  <Button
                    className="w-full transition-all duration-300 overflow-hidden relative"
                    variant="outline"
                    disabled={true}
                  >
                    <span className="relative z-10">Not Available</span>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
