import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Globe,
  AlertCircle,
  Loader2,
  Search,
  BookOpen,
  FileText
} from "lucide-react";
import { useState } from "react";
import { useNewsletterSubscription } from "@/hooks/use-newsletter-subscription";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";

export function Footer() {
  const [email, setEmail] = useState("");
  const [subscribed, setSubscribed] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();

  const { mutate: subscribeToNewsletter, isPending } = useNewsletterSubscription();

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError("Please enter your email address");
      return;
    } else if (!/^\S+@\S+\.\S+$/.test(email)) {
      setError("Please enter a valid email address");
      return;
    }

    // Send the subscription request to the backend
    subscribeToNewsletter(
      { email },
      {
        onSuccess: (data) => {
          setSubscribed(true);
          setError("");
          toast({
            title: "Success!",
            description: data.message,
            variant: "default",
          });
        },
        onError: (error) => {
          setError(error.message || "Failed to subscribe. Please try again.");
          toast({
            title: "Error",
            description: error.message || "Failed to subscribe. Please try again.",
            variant: "destructive",
          });
        },
      }
    );
  };

  return (
    <footer className="mt-12 sm:mt-20 relative overflow-hidden">
      <Card className="bg-white shadow-md w-full">
        <CardContent className="p-6 sm:p-10">
          {/* Background elements */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-teal-500"></div>
          <div className="absolute -top-16 -left-16 w-32 h-32 rounded-full bg-gradient-to-br from-primary-100 to-transparent opacity-30"></div>
          <div className="absolute -bottom-20 -right-20 w-40 h-40 rounded-full bg-gradient-to-tr from-secondary-100 to-transparent opacity-30"></div>

          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-10">
              {/* Brand section */}
              <div className="flex flex-col space-y-4">
                <a href="/" className="flex items-center gap-2 group">
                  <div className="w-10 h-10 rounded-lg overflow-hidden shadow-lg">
                    <img src="/domainmate.png" alt="DomainMate Logo" className="w-full h-full object-cover" />
                  </div>
                  <div>
                    <span className="text-xl font-bold text-gradient">
                      DomainMate
                    </span>
                  </div>
                </a>
                <p className="text-gray-600 text-sm mt-2 max-w-xs">
                  AI-powered domain name generator to find the perfect domain for your next project.
                </p>

                {/* Social icons section */}
                <div className="flex mt-4 space-x-2 sm:space-x-3">
                  <a href="https://twitter.com/domain_mate" target="_blank" rel="noopener noreferrer" className="w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md">
                    <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
                  </a>
                  <a href="https://www.linkedin.com/company/domain-mate" target="_blank" rel="noopener noreferrer" className="w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md">
                    <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>
                  </a>
                  <a href="https://instagram.com/domain_mate" target="_blank" rel="noopener noreferrer" className="w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md">
                    <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
                  </a>
                  <a href="https://www.tiktok.com/@domain_mate" target="_blank" rel="noopener noreferrer" className="w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md">
                    <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"></path><path d="M15 8v6c0 5-4 5-6 5a7 7 0 0 1-3-1"></path><path d="M15 2v6m-3 3c5.7 0 6.4-4 7-6"></path></svg>
                  </a>
                </div>
              </div>

              {/* Domain Resources */}
              <div>
                <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4">
                  Domain Resources
                </h3>
                <ul className="space-y-3">
                  <li>
                    <Link href="/" className="text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1">
                      <Search className="w-4 h-4" />
                      <span>Generate Domain Names</span>
                    </Link>
                  </li>
                  <li>
                    <Link href="/blog" className="text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1.5 group">
                      <BookOpen className="w-4 h-4 group-hover:rotate-3 transition-transform duration-300" />
                      <span>DomainMate Blog</span>
                    </Link>
                  </li>
                  <li>
                    <a href="/#faq" className="text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1">
                      <FileText className="w-4 h-4" />
                      <span>Domain Name Generator FAQ</span>
                    </a>
                  </li>
                  <li>
                    <a href="https://namecheap.pxf.io/domainmate" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1">
                      <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path></svg>
                      <span>Namecheap Domain Registrar</span>
                    </a>
                  </li>
                  <li>
                    <a href="https://namecheap.pxf.io/c/6159477/624623/5618" target="_blank" rel="sponsored noopener" className="text-green-600 hover:text-green-700 font-medium transition-colors text-sm flex items-center gap-1">
                      <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83"></path></svg>
                      <span>Special: 56% off 1st yr Shared Hosting!</span>
                    </a>
                    <div style={{position: 'absolute', visibility: 'hidden'}}>
                      <img height="0" width="0" src="https://namecheap.pxf.io/c/6159477/624623/5618" style={{border: '0'}} alt="Namecheap affiliate tracking pixel" />
                    </div>
                  </li>
                </ul>
              </div>

              {/* Newsletter section */}
              <div>
                <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4">
                  Subscribe to our newsletter
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  Get the latest updates, news and special offers.
                </p>

                {subscribed ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 animate-scale-in">
                    <p className="text-green-700 text-sm font-medium">
                      Thank you for subscribing!
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubscribe} className="mt-2">
                    <div className="flex flex-col gap-2">
                      <Input
                        type="email"
                        placeholder="Your email address"
                        className={`bg-gray-50 w-full ${error ? 'border-red-500' : ''}`}
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);
                          if (error) setError("");
                        }}
                      />
                      <Button
                        type="submit"
                        variant="default"
                        className="bg-primary-600 hover:bg-primary-700 text-white whitespace-nowrap"
                        disabled={isPending}
                      >
                        {isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            <span>Subscribing...</span>
                          </>
                        ) : (
                          <span>Subscribe</span>
                        )}
                      </Button>
                    </div>

                    {error && (
                      <div className="mt-2 flex items-center text-red-600 text-xs animate-slide-up">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        <span>{error}</span>
                      </div>
                    )}
                  </form>
                )}
              </div>
            </div>

            <div className="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-gray-200 text-center">
              <div className="flex justify-center space-x-4 mb-4">
                <a href="/privacy-policy" className="text-gray-500 text-sm hover:text-primary-600 transition-colors">
                  Privacy Policy
                </a>
                <a href="/terms-of-service" className="text-gray-500 text-sm hover:text-primary-600 transition-colors">
                  Terms of Service
                </a>
              </div>
              <p className="text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} DomainMate. All rights reserved.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </footer>
  );
}
