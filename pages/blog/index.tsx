import Head from 'next/head';
import { GetStaticProps } from 'next';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { blogPosts } from "@/lib/blog-posts";
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface BlogPageProps {
  posts: typeof blogPosts;
}

export default function BlogPage({ posts }: BlogPageProps) {
  return (
    <>
      <Head>
        <title>Domain Name Blog - Tips & Guides | DomainMate</title>
        <meta
          name="description"
          content="Expert tips and guides on domain names, branding, and online presence. Learn how to choose the perfect domain for your business."
        />
        <meta
          name="keywords"
          content="domain name blog, domain tips, branding guide, domain selection, online presence, domain strategy"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="Domain Name Blog - Tips & Guides | DomainMate" />
        <meta property="og:description" content="Expert tips and guides on domain names, branding, and online presence. Learn how to choose the perfect domain for your business." />
        <meta property="og:url" content="https://domainmate.net/blog" />
        <meta property="og:type" content="website" />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Domain Name Blog - Tips & Guides | DomainMate" />
        <meta name="twitter:description" content="Expert tips and guides on domain names, branding, and online presence. Learn how to choose the perfect domain for your business." />

        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/blog" />

        {/* Structured Data for Blog */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Blog",
              "name": "DomainMate Blog",
              "description": "Expert tips and guides on domain names, branding, and online presence",
              "url": "https://domainmate.net/blog",
              "publisher": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              },
              "blogPost": posts.map(post => ({
                "@type": "BlogPosting",
                "headline": post.title,
                "description": post.excerpt,
                "url": `https://domainmate.net/blog/${post.id}`,
                "datePublished": post.date,
                "author": {
                  "@type": "Organization",
                  "name": "DomainMate"
                }
              }))
            })
          }}
        />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="mb-8">
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Domain Name Blog
              </h1>
              <p className="text-lg text-gray-600 max-w-3xl">
                Expert tips and guides on domain names, branding, and building a strong online presence for your business.
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {posts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-xl">
                      <Link
                        href={`/blog/${post.id}`}
                        className="text-gray-800 hover:text-blue-600 transition-colors"
                      >
                        {post.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{new Date(post.date).toLocaleDateString()}</span>
                      <span>{post.readTime}</span>
                    </div>
                    <Link
                      href={`/blog/${post.id}`}
                      className="inline-block mt-4 text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Read more →
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>

            {posts.length === 0 && (
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                  Coming Soon
                </h2>
                <p className="text-gray-600">
                  We're working on creating valuable content about domain names and branding. Check back soon!
                </p>
              </div>
            )}
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

// This function gets called at build time for static generation
export const getStaticProps: GetStaticProps<BlogPageProps> = async () => {
  return {
    props: {
      posts: blogPosts,
    },
  };
};
