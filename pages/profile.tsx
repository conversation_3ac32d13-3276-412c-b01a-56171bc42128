import Head from 'next/head';
import { GetStaticProps } from 'next';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { ProtectedRoute } from "@/components/protected-route";
import { UserProfile } from "@/components/user-profile";
import { FavoritesSection } from "@/components/favorites-section";

interface ProfilePageProps {
  // Add any static props here if needed
}

export default function ProfilePage({}: ProfilePageProps) {
  return (
    <>
      <Head>
        <title>My Profile | DomainMate</title>
        <meta
          name="description"
          content="Manage your DomainMate profile, view your favorite domains, and track your domain search history."
        />
        <meta name="robots" content="noindex, nofollow" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/profile" />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <ProtectedRoute>
              <div className="mb-8">
                <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                  My Profile
                </h1>
                <p className="text-lg text-gray-600">
                  Manage your account and view your favorite domains.
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                <div className="lg:col-span-1">
                  <UserProfile />
                </div>

                <div className="lg:col-span-2">
                  <FavoritesSection />
                </div>
              </div>
            </ProtectedRoute>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

// This function gets called at build time for static generation
export const getStaticProps: GetStaticProps<ProfilePageProps> = async () => {
  return {
    props: {},
  };
};
