# Security Guidelines for DomainMate

## Environment Variables Security

### Critical: Never Deploy .env Files
- The `.env` file contains sensitive credentials and should NEVER be deployed to production
- Always set environment variables directly on the production server
- Use secrets management systems in production environments

### Required Environment Variables for Production
```bash
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# AI Services
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
AI_PROVIDER=openai

# Authentication
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier

# Client-side (for static builds)
NEXT_PUBLIC_AUTH0_DOMAIN=your-domain.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-client-id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://your-api-identifier
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## Deployment Security

### Manual Deployment
1. Use the updated `scripts/deploy.sh` script
2. Set environment variables directly on the server after deployment
3. Never copy `.env` files to production

### Automated Deployment (GitHub Actions)
- Uses GitHub Secrets for sensitive data
- Properly handles environment variables without exposing them in logs
- Creates client-side config at build time

## API Security Features

### Authentication
- JWT validation with Auth0
- Rate limiting on JWKS requests
- Proper algorithm restriction (RS256 only)

### API Protection
- Custom header validation (`X-App-Request`)
- Origin validation
- CORS configuration
- Development mode bypass for testing

### Security Headers
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block

## Security Checklist

### Before Deployment
- [ ] Remove any `.env` files from deployment packages
- [ ] Verify secrets are not committed to version control
- [ ] Check that API keys are properly secured
- [ ] Validate authentication middleware is working
- [ ] Test rate limiting and API protection

### Production Server Setup
- [ ] Set environment variables securely
- [ ] Use HTTPS for all communications
- [ ] Configure firewall rules
- [ ] Enable fail2ban or similar intrusion prevention
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity

## Incident Response

If you suspect a security breach:
1. Immediately rotate all API keys and database credentials
2. Check server logs for unauthorized access
3. Update all authentication tokens
4. Review and update security measures
5. Document the incident and response actions 