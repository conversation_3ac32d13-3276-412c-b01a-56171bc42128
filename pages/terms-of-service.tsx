import Head from 'next/head';
import { GetStaticProps } from 'next';
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { Card, CardContent } from "@/components/ui/card";

interface TermsOfServicePageProps {
  lastUpdated: string;
}

export default function TermsOfServicePage({ lastUpdated }: TermsOfServicePageProps) {
  return (
    <>
      <Head>
        <title>Terms of Service | DomainMate</title>
        <meta
          name="description"
          content="Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service."
        />
        <meta name="robots" content="index, follow" />

        {/* Open Graph tags */}
        <meta property="og:title" content="Terms of Service | DomainMate" />
        <meta property="og:description" content="Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service." />
        <meta property="og:url" content="https://domainmate.net/terms-of-service" />
        <meta property="og:type" content="website" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://domainmate.net/terms-of-service" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebPage",
              "name": "Terms of Service",
              "description": "DomainMate's terms of service explaining the rules and guidelines for using our service",
              "url": "https://domainmate.net/terms-of-service",
              "publisher": {
                "@type": "Organization",
                "name": "DomainMate",
                "url": "https://domainmate.net"
              },
              "dateModified": lastUpdated
            })
          }}
        />
      </Head>

      <div className="font-sans text-gray-700 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <header className="mb-10">
            <Navbar />
          </header>

          <main>
            <div className="mb-8">
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Terms of Service
              </h1>
              <p className="text-lg text-gray-600">
                Last updated: {new Date(lastUpdated).toLocaleDateString()}
              </p>
            </div>

            <Card>
              <CardContent className="p-8">
                <div className="prose prose-lg max-w-none">
                  <h2>Acceptance of Terms</h2>
                  <p>
                    By accessing and using DomainMate, you accept and agree to be bound by the terms
                    and provision of this agreement.
                  </p>

                  <h2>Description of Service</h2>
                  <p>
                    DomainMate is an AI-powered domain name generator that helps users find available
                    domain names for their businesses and projects. Our service includes:
                  </p>
                  <ul>
                    <li>AI-generated domain name suggestions</li>
                    <li>Domain availability checking</li>
                    <li>Bulk domain checking</li>
                    <li>Favorites management</li>
                    <li>User accounts and profiles</li>
                  </ul>

                  <h2>User Accounts</h2>
                  <p>
                    To access certain features of our service, you may be required to create an account.
                    You are responsible for maintaining the confidentiality of your account information.
                  </p>

                  <h2>Acceptable Use</h2>
                  <p>
                    You agree to use our service only for lawful purposes and in accordance with these Terms.
                    You agree not to:
                  </p>
                  <ul>
                    <li>Use the service for any illegal or unauthorized purpose</li>
                    <li>Attempt to gain unauthorized access to our systems</li>
                    <li>Interfere with or disrupt the service</li>
                    <li>Use automated tools to access the service excessively</li>
                  </ul>

                  <h2>Intellectual Property</h2>
                  <p>
                    The service and its original content, features, and functionality are and will remain
                    the exclusive property of DomainMate and its licensors.
                  </p>

                  <h2>Domain Registration</h2>
                  <p>
                    DomainMate does not register domains on behalf of users. We provide suggestions and
                    availability information, but domain registration must be completed through authorized
                    domain registrars.
                  </p>

                  <h2>Disclaimer of Warranties</h2>
                  <p>
                    The information, software, products, and services included in or available through
                    the service may include inaccuracies or typographical errors. We make no warranty
                    that the service will be uninterrupted or error-free.
                  </p>

                  <h2>Limitation of Liability</h2>
                  <p>
                    In no event shall DomainMate be liable for any indirect, incidental, special,
                    consequential, or punitive damages arising out of your use of the service.
                  </p>

                  <h2>Rate Limiting</h2>
                  <p>
                    To ensure fair usage and maintain service quality, we implement rate limiting on
                    our API endpoints. Excessive usage may result in temporary restrictions.
                  </p>

                  <h2>Privacy</h2>
                  <p>
                    Your privacy is important to us. Please review our Privacy Policy, which also
                    governs your use of the service.
                  </p>

                  <h2>Termination</h2>
                  <p>
                    We may terminate or suspend your account and bar access to the service immediately,
                    without prior notice or liability, under our sole discretion, for any reason whatsoever.
                  </p>

                  <h2>Changes to Terms</h2>
                  <p>
                    We reserve the right to modify or replace these Terms at any time. If a revision
                    is material, we will provide at least 30 days notice prior to any new terms taking effect.
                  </p>

                  <h2>Contact Information</h2>
                  <p>
                    If you have any questions about these Terms of Service, please contact us through
                    our website.
                  </p>
                </div>
              </CardContent>
            </Card>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
}

// This function gets called at build time for static generation
export const getStaticProps: GetStaticProps<TermsOfServicePageProps> = async () => {
  return {
    props: {
      lastUpdated: new Date().toISOString(),
    },
  };
};
