import { useState, useEffect, useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import type { DomainInfo, GroupedDomainInfo } from "@shared/types";
import { getSupportedTldNames } from "@shared/tld-config";
import { useToast } from "@/hooks/use-toast";
import { useRateLimit } from "@/hooks/use-rate-limit";
import { useAnalytics } from "@/hooks/use-analytics";
import { apiFetch } from "@/lib/api-helpers";
import { DOMAIN_GENERATION_COUNT } from "@/config/ai-config";

// Type definitions for API responses
interface TldsResponse {
  tlds: string[];
}

interface DomainQueryData {
  domains: DomainInfo[];
  searchTerm: string;
  timestamp: string;
}

export type DomainFilterOption = "all" | "available";
export type SearchStatus = "idle" | "generating" | "checking" | "done";

export const useDomainSearch = () => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterBy, setFilterBy] = useState<DomainFilterOption>("all");
  const [selectedTlds, setSelectedTlds] = useState<string[]>([]);
  const [searchStatus, setSearchStatus] = useState<SearchStatus>("idle");
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { trackEvent } = useAnalytics();

  // Get rate limit information
  const { rateLimitInfo, isLimited, refetch: refetchRateLimit } = useRateLimit();

  // Get TLDs
  const { data: tldsData } = useQuery<TldsResponse>({
    queryKey: ['/api/domains/tlds'],
    // Important: Use placeholders if query fails - use the shared TLD configuration
    placeholderData: { tlds: getSupportedTldNames() }
  });

  // Get domain suggestions
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery<DomainQueryData>({
    queryKey: ['/api/domains/search', searchTerm, selectedTlds],
    enabled: searchTerm.length > 1,
    // Apply the TLD filter to the URL parameters when making the API request
    meta: {
      // Convert the selected TLDs to a comma-separated string for the API
      queryParams: {
        term: searchTerm,
        tlds: selectedTlds.join(','),
        limit: DOMAIN_GENERATION_COUNT.toString(),
      }
    }
  });

  // Initialize TLDs when they become available
  useEffect(() => {
    if (selectedTlds.length === 0 && tldsData?.tlds && tldsData.tlds.length > 0) {
      setSelectedTlds(tldsData.tlds);
    }
  }, [tldsData, selectedTlds.length]);

  // Update search status based on loading state
  useEffect(() => {
    if (isLoading && searchTerm.length > 1) {
      setSearchStatus("generating");
    } else if (searchTerm.length > 1 && !isLoading) {
      setSearchStatus("done");
    }
  }, [isLoading, searchTerm]);

  // Search for domains
  const searchDomains = async (term: string) => {
    if (!term || term.length < 2) return;


    await refetchRateLimit();

    if (isLimited) {

      toast({
        title: "Rate Limit Reached",
        description: `You've reached the limit of ${rateLimitInfo.total} searches per hour. Please try again after ${new Date(rateLimitInfo.resetTime).toLocaleTimeString()}.`,
        variant: "destructive",
      });
      return;
    }

    setSearchTerm(term);
    setSearchStatus("generating");

    try {

      if (selectedTlds.length === 0 && tldsData?.tlds) {
        setSelectedTlds(tldsData.tlds);
      }


      await queryClient.invalidateQueries({
        queryKey: ['/api/domains/search', term, selectedTlds]
      });


      await refetch();

      // Track successful domain search in analytics
      trackEvent('domain_search', {
        search_term: term,
        tlds: selectedTlds.join(','),
        num_tlds: selectedTlds.length
      });

      refetchRateLimit();
    } catch (error) {
      // Create a user-friendly error message
      let errorMessage = "Error searching domains. Please try again.";

      if (error instanceof Error) {
        if (error.message.includes("400")) {
          errorMessage = "Your search description may be too long or contains invalid characters. Please try a shorter description.";
        } else if (error.message.includes("429")) {
          errorMessage = "Rate limit exceeded. Please try again later.";

          refetchRateLimit();
        }
      }

      // Update the query client with the error
      queryClient.setQueryData(
        ['/api/domains/search', term, selectedTlds],
        { error: errorMessage, domains: [], searchTerm: term, timestamp: new Date().toISOString() }
      );

      // Show toast notification for the error
      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });

      setSearchStatus("done");
    }
  };

  // Generate more domain suggestions
  const generateMoreSuggestions = async () => {
    if (!searchTerm || searchTerm.length < 2) return;

    // Track the event when user requests more suggestions
    trackEvent('generate_more_domains', {
      search_term: searchTerm
    });

    // Check rate limit before proceeding
    await refetchRateLimit();

    if (isLimited) {
      // Show toast notification about rate limit
      toast({
        title: "Rate Limit Reached",
        description: `You've reached the limit of ${rateLimitInfo.total} searches per hour. Please try again after ${new Date(rateLimitInfo.resetTime).toLocaleTimeString()}.`,
        variant: "destructive",
      });
      return;
    }

    setSearchStatus("generating");

    try {
      // Use the same API request mechanism that the queryFn uses
      // Use our enhanced fetch function that automatically includes the app request header
      // We don't need a regenerate parameter anymore since we've removed caching
      // Each search now always produces fresh results from the server
      const response = await apiFetch(`/api/domains/search?term=${encodeURIComponent(searchTerm)}&tlds=${encodeURIComponent(selectedTlds.join(','))}&limit=${DOMAIN_GENERATION_COUNT}`);

      if (!response.ok) {
        const errorText = await response.text();
        // Handle API error with user-friendly message

        // Format the error message in a user-friendly way
        let errorMessage = "Failed to generate more suggestions. Please try again.";

        if (response.status === 400) {
          errorMessage = "Your search description may be too long or contains invalid characters. Please try a shorter description.";
        } else if (response.status === 429) {
          errorMessage = "Rate limit exceeded. Please try again later.";
          // Refresh rate limit info
          refetchRateLimit();
        } else if (response.status === 500) {
          errorMessage = "Server error while generating suggestions. Please try again later.";
        }

        // Get current data from query cache
        const currentData = queryClient.getQueryData(['/api/domains/search', searchTerm, selectedTlds]) as DomainQueryData | undefined;

        // Update the query with the error
        queryClient.setQueryData(
          ['/api/domains/search', searchTerm, selectedTlds],
          {
            error: errorMessage,
            domains: currentData?.domains || [],
            searchTerm,
            timestamp: new Date().toISOString()
          }
        );

        // Show toast notification for the error
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });

        throw new Error(`API responded with ${response.status}: ${errorText || 'Unknown error'}`);
      }

      const responseData = await response.json();
      console.log("Received new domain suggestions:", responseData);

      // Get current data from query cache
      const currentData = queryClient.getQueryData(['/api/domains/search', searchTerm, selectedTlds]) as DomainQueryData | undefined;

      // Combine the existing domains with the new ones, deduplicating by fullDomain
      const existingDomains = currentData?.domains || [];
      const newDomains = responseData.domains as DomainInfo[];

      // Create a map of existing domains by fullDomain for quick lookup
      const existingDomainMap = new Map<string, DomainInfo>();
      existingDomains.forEach(domain => {
        existingDomainMap.set(domain.fullDomain, domain);
      });

      // Add new domains only if they don't already exist or if they're available and the existing one isn't
      const combinedDomains: DomainInfo[] = [...existingDomains];

      newDomains.forEach(newDomain => {
        const existingDomain = existingDomainMap.get(newDomain.fullDomain);

        if (!existingDomain) {
          // Domain doesn't exist yet, add it
          combinedDomains.push(newDomain);
        } else if (newDomain.available && !existingDomain.available) {
          // Replace the existing domain if the new one is available and the existing one isn't
          const index = combinedDomains.findIndex(d => d.fullDomain === newDomain.fullDomain);
          if (index !== -1) {
            combinedDomains[index] = newDomain;
          }
        }
      });

      const combinedData = {
        ...responseData,
        domains: combinedDomains
      };

      // Manually update the cache with the combined data
      queryClient.setQueryData(
        ['/api/domains/search', searchTerm, selectedTlds],
        combinedData
      );

      // Refresh rate limit info after successful request
      refetchRateLimit();

      setSearchStatus("done");
    } catch (error) {
      console.error("Error generating more domain suggestions:", error);
      setSearchStatus("done");
    }
  };

  // Filter domains based on availability and selected TLDs
  const filteredDomains = () => {
    if (!data?.domains) return [];

    // Filter first by availability, then by selected TLDs
    return data.domains.filter((domain: DomainInfo) => {
      const isAvailable = domain.available;
      // If TLDs are selected, check if the domain's TLD is included
      const matchesTld = selectedTlds.length > 0
        ? selectedTlds.includes(domain.tld)
        : true; // If no TLDs are selected, show all

      return isAvailable && matchesTld;
    });
  };

  // Group domains by base name
  const groupDomains = (domains: DomainInfo[]): GroupedDomainInfo[] => {
    // Create a map to group domains by their base name
    const domainGroups = new Map<string, Map<string, DomainInfo>>();

    // Group domains by their base name and deduplicate by TLD
    domains.forEach(domain => {
      // Get or create the group for this base name
      if (!domainGroups.has(domain.name)) {
        domainGroups.set(domain.name, new Map<string, DomainInfo>());
      }

      const tldMap = domainGroups.get(domain.name)!;

      // Only add this domain if we don't already have this TLD for this base name
      // or if the existing one is not available but the new one is
      const existingDomain = tldMap.get(domain.tld);
      if (!existingDomain || (!existingDomain.available && domain.available)) {
        tldMap.set(domain.tld, domain);
      }
    });

    // Convert the map to an array of GroupedDomainInfo objects
    return Array.from(domainGroups.entries()).map(([baseName, tldMap]) => {
      // Convert the TLD map to an array of domains
      const domains = Array.from(tldMap.values());

      // Split domains into available and unavailable
      const availableDomains = domains.filter(d => d.available);
      const unavailableDomains = domains.filter(d => !d.available);

      // Get the lowest price from available domains
      const lowestPrice = availableDomains.length > 0
        ? Math.min(...availableDomains.filter(d => d.price !== undefined).map(d => {
            const price = d.price;
            if (typeof price === 'string') {
              const parsed = parseFloat(price);
              return isNaN(parsed) ? Infinity : parsed;
            }
            return typeof price === 'number' ? price : Infinity;
          }))
        : undefined;

      // Use the type from the first domain as the group type
      const type = domains[0]?.type;

      return {
        baseName,
        domains,
        availableTlds: availableDomains.map(d => d.tld),
        unavailableTlds: unavailableDomains.map(d => d.tld),
        lowestPrice: lowestPrice === Infinity ? undefined : lowestPrice,
        type
      };
    });
  };

  // Get domains with default sorting (available first, then alphabetical)
  const getDomains = () => {
    const domains = filteredDomains();

    // Default sorting: available domains first, then sort by name
    return [...domains].sort((a: DomainInfo, b: DomainInfo) => {
      if (a.available !== b.available) {
        return a.available ? -1 : 1;
      }
      return a.fullDomain.localeCompare(b.fullDomain);
    });
  };

  // Get grouped domains
  const groupedDomains = useMemo(() => {
    const domains = getDomains();
    return groupDomains(domains);
  }, [selectedTlds, filterBy, data?.domains]);

  // Sort grouped domains
  const sortedGroupedDomains = useMemo(() => {
    return [...groupedDomains].sort((a, b) => {
      // Sort by available TLDs count first (more is better)
      if (a.availableTlds.length !== b.availableTlds.length) {
        return b.availableTlds.length - a.availableTlds.length;
      }

      // Then by base name alphabetically
      return a.baseName.localeCompare(b.baseName);
    });
  }, [groupedDomains]);

  // Get domain statistics
  const getDomainStats = () => {
    if (!data?.domains) {
      return { available: 0, unavailable: 0 };
    }

    const available = data.domains.filter((d: DomainInfo) => d.available).length;
    const unavailable = data.domains.length - available;

    return { available, unavailable };
  };

  return {
    searchTerm,
    setSearchTerm,
    searchDomains,
    isLoading,
    error,
    data,
    domains: getDomains(),
    groupedDomains: sortedGroupedDomains,
    filterBy,
    setFilterBy,
    stats: getDomainStats(),
    hasMadeSearch: searchTerm.length > 0,
    popularTlds: tldsData?.tlds || [],
    selectedTlds,
    setSelectedTlds,
    searchStatus,
    refetch, // Export the refetch function
    generateMoreSuggestions, // Add the new function to generate more suggestions
  };
};
