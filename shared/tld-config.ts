/**
 * Shared TLD configuration
 * This file contains configuration values for Top-Level Domains (TLDs)
 * that are shared between client and server to ensure consistency
 */

/**
 * Interface for TLD configuration
 */
export interface TldConfig {
  /** The TLD without the dot (e.g., 'com', 'net') */
  name: string;
  /** The price for domain registration in USD */
  price: number;
  /** CSS classes for styling the TLD badge */
  style: string;
  /** Category of the TLD (generic, new, etc.) */
  category: 'generic' | 'new';
}

/**
 * List of supported TLDs with their configuration
 */
export const SUPPORTED_TLDS: TldConfig[] = [
  // Generic TLDs
  {
    name: 'com',
    price: 6.49,
    style: 'bg-primary-100 text-primary-700 border-primary-200',
    category: 'generic'
  },
  {
    name: 'net',
    price: 10.98,
    style: 'bg-teal-100 text-teal-700 border-teal-200',
    category: 'generic'
  },
  {
    name: 'org',
    price: 7.48,
    style: 'bg-green-100 text-green-700 border-green-200',
    category: 'generic'
  },
  {
    name: 'io',
    price: 34.98,
    style: 'bg-violet-100 text-violet-700 border-violet-200',
    category: 'generic'
  },

  // New gTLDs
  {
    name: 'app',
    price: 12.98,
    style: 'bg-fuchsia-100 text-fuchsia-700 border-fuchsia-200',
    category: 'new'
  },
  {
    name: 'dev',
    price: 10.98,
    style: 'bg-emerald-100 text-emerald-700 border-emerald-200',
    category: 'new'
  },
  {
    name: 'me',
    price: 9.98,
    style: 'bg-orange-100 text-orange-700 border-orange-200',
    category: 'new'
  },
  {
    name: 'shop',
    price: 1.28,
    style: 'bg-amber-100 text-amber-700 border-amber-200',
    category: 'new'
  },
  {
    name: 'online',
    price: 0.98,
    style: 'bg-indigo-100 text-indigo-700 border-indigo-200',
    category: 'new'
  }
];

/**
 * Default price for TLDs not explicitly configured
 */
export const DEFAULT_TLD_PRICE = 14.99;

/**
 * Get the names of all supported TLDs
 * @returns Array of TLD names without the dot
 */
export function getSupportedTldNames(): string[] {
  return SUPPORTED_TLDS.map(tld => tld.name);
}

/**
 * Get the price for a specific TLD
 * @param tld The TLD name with or without the dot (e.g., 'com', '.com')
 * @returns The price for the TLD in USD
 */
export function getTldPrice(tld: string): number {
  // Remove the dot if present
  const cleanTld = tld.startsWith('.') ? tld.substring(1) : tld;
  const tldConfig = SUPPORTED_TLDS.find(config => config.name === cleanTld.toLowerCase());
  return tldConfig?.price || DEFAULT_TLD_PRICE;
}

/**
 * Get the style for a specific TLD
 * @param tld The TLD name with or without the dot (e.g., 'com', '.com')
 * @returns CSS classes for styling the TLD badge
 */
export function getTldStyle(tld: string): string {
  // Remove the dot if present
  const cleanTld = tld.startsWith('.') ? tld.substring(1) : tld;
  const tldConfig = SUPPORTED_TLDS.find(config => config.name === cleanTld.toLowerCase());
  return tldConfig?.style || 'bg-gray-100 text-gray-700 border-gray-200';
}

/**
 * Validate if a TLD is supported
 * @param tld The TLD name with or without the dot (e.g., 'com', '.com')
 * @returns True if the TLD is supported, false otherwise
 */
export function isSupportedTld(tld: string): boolean {
  // Remove the dot if present
  const cleanTld = tld.startsWith('.') ? tld.substring(1) : tld;
  return SUPPORTED_TLDS.some(config => config.name === cleanTld.toLowerCase());
}

/**
 * Generate a regex pattern that matches all supported TLDs
 * @returns A regex pattern string for use in validation
 */
export function getSupportedTldRegexPattern(): string {
  const tldNames = getSupportedTldNames();
  return `(${tldNames.join('|')})`;
}
