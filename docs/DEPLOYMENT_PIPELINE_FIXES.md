# Deployment Pipeline Fixes for SSG Migration

## Summary

The deployment pipeline has been successfully updated to support the SSG (Static Site Generation) architecture. The changes ensure that DomainMate is deployed as a static site with a minimal server for serving files.

## Changes Made

### 1. Updated GitHub Actions Workflow (`.github/workflows/deploy.yml`)

#### Build Process Changes
- **Removed server build**: No longer builds Express.js server bundle
- **Environment variables**: Set at build time for Next.js SSG
- **Static generation**: Only runs `npm run build` to generate static files
- **Runtime config**: Creates `config.js` for dynamic configuration

#### Deployment Package Changes
- **Static files only**: Only includes `out/` directory with static site
- **Minimal server**: Simple Express.js server for serving static files
- **Removed dependencies**: No longer copies server-side code, migrations, or database files

#### Server Configuration Changes
- **Updated systemd service**: Points to static file server instead of full application
- **Working directory**: Changed to `/root/domainmate/server-static`
- **Entry point**: Simple `server.js` that serves static files
- **Minimal dependencies**: Only installs Express.js for static serving

### 2. Updated Package.json Scripts

```json
{
  "build": "next build",
  "build:server": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist"
}
```

- **Separated concerns**: SSG build vs server build
- **Default build**: Now only builds static site
- **Server build**: Available separately if needed

### 3. Created Static File Server

The deployment now includes a minimal Express.js server (`server-static/server.js`) that:
- Serves static files from the `out/` directory
- Handles client-side routing by serving `index.html` for all routes
- Runs on port 3000
- Has minimal dependencies (only Express.js)

### 4. Runtime Configuration

- **Build-time variables**: Set during Next.js build process
- **Runtime variables**: Injected via `config.js` script
- **Dynamic configuration**: Allows environment-specific settings without rebuilding

## Deployment Flow

1. **Build**: Generate static site with `npm run build`
2. **Configure**: Create runtime configuration file
3. **Package**: Bundle static files and minimal server
4. **Deploy**: Transfer to VPS and start static file server
5. **Serve**: Simple Express.js server serves static files

## Benefits

### Performance
- ✅ Faster page loads (pre-rendered HTML)
- ✅ Better SEO (static HTML content)
- ✅ Reduced server load (no server-side rendering)

### Deployment
- ✅ Simpler deployment (static files + minimal server)
- ✅ Faster deployments (smaller package size)
- ✅ More reliable (fewer moving parts)

### Maintenance
- ✅ Easier debugging (static files)
- ✅ Lower resource usage (minimal server)
- ✅ Better caching (static assets)

## Testing

### Local Testing
```bash
# Build and test locally
npm run build
cd out && python -m http.server 8080

# Or use the test script
./scripts/test-ssg-build.sh
```

### Production Testing
```bash
# Check systemd service
sudo systemctl status domainmate.service

# View logs
sudo journalctl -u domainmate.service -f

# Test endpoints
curl http://localhost:3000
curl http://localhost:3000/blog
```

## Verification Checklist

- ✅ SSG build generates static files in `out/` directory
- ✅ All pages are pre-rendered (16 pages generated)
- ✅ Runtime configuration is properly injected
- ✅ Static server serves files correctly
- ✅ Client-side routing works
- ✅ Environment variables are handled correctly
- ✅ Deployment package contains only necessary files
- ✅ Systemd service configuration is correct

## Next Steps

1. **Test deployment**: Push changes to trigger GitHub Actions
2. **Monitor logs**: Check deployment and service logs
3. **Verify functionality**: Test all features on production
4. **Performance monitoring**: Monitor page load times and SEO metrics

## Rollback Plan

If issues occur, the previous server-based deployment can be restored by:
1. Reverting the workflow changes
2. Using `npm run build:server` to build the Express.js server
3. Updating systemd service to point to `dist/index.js`

The SSG migration maintains all existing functionality while improving performance and deployment reliability.
